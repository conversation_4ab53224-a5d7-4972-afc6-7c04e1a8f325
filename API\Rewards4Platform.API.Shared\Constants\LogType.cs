﻿namespace Rewards4Platform.API.Shared.Constants
{
    public class LogType
    {
        public static int PageViewed = 1;
        public static int MemberDeepLinkLogin = 2;
        public static int MemberLogin = 3;
        public static int MemberSubscribe = 4;
        public static int MemberUnsubscribe = 5;
        public static int OnlineShoppingClick = 6;
        public static int TermsAndConditionsAccepted = 7;
        public static int LoggedInViaFacebook = 8;
        public static int RegisteredViaFacebook = 9;
        public static int ReferedAFriend = 10;
        public static int PointsCalculation = 11;
        public static int ContactedCustomerService = 13;
        public static int ContentShared = 14;
        public static int LoggedInViaBooster = 15;
        public static int DownloadedBooster = 16;
        public static int ClickedThroughFromBooster = 17;
        public static int SearchedUsingBooster = 18;
        public static int ShoppedOnline = 19;
        public static int BettingSignUp = 20;
        public static int Redeemed = 21;
        public static int CompletedSurvey = 22;
        public static int MissingPointsClaim = 23;
        public static int EnteredCompetition = 24;
        public static int BettingPointsClaimed = 25;
        public static int RequestPointsReminderEmail = 26;
        public static int ClickedThroughToRetailerFromEmail = 27;
        public static int RequestRetailerComingSoonEmail = 28;
        public static int CoralClick = 29;
        public static int Bet365Click = 30;
        public static int BetfedClick = 31;
        public static int RegisteredViaWebsite = 32;
        public static int RequestToolsToCollectReminderEmail = 33;
        public static int FailedSignIn = 34;
        public static int FailedRegistration = 35;
        public static int PasswordActivated = 38;
        public static int MemberLandingPage = 45;
        public static int OptionalClubPopupDisplayed = 46;
        public static int LoggedInViaGoogle = 47;
        public static int RegisteredViaGoogle = 48;
        public static int JCRClaimPasswordSet = 49;
        public static int ARCYorkClaimPasswordSet = 50;
        public static int RflPredictorWinnerPointsClaim = 51;
        public static int LancashireClaimPointsPasswordSet = 53;
        public static int AcceptedTermsAndConditionsUpdateOnWebsite = 55;
        public static int RFLOurLeagueAppClaimPoints = 60;
        public static int RetailerSearchByMember = 63;
        public static int MemberChangedTheirPassword = 64;
        public static int MemberFailedToChangeTheirPassword = 65;
        public static int MemberResetTheirPassword = 66;
        public static int MemberFailedToResetTheirPassword = 67;
        public static int RacingClaimPointsPasswordSet = 68;
        public static int Bet365LiveOddsClickthrough = 69;
        public static int BetfairLiveOddsClickthrough = 70;
        public static int BetredLiveOddsClickthrough = 71;
        public static int AccountTransferToRewards4Sport = 72;
        public static int RacingBet365PromotionSignUpMember = 73;
        public static int CompleteProfilePopupViewed = 74;
        public static int OptedOutCompleteProfilePopupView = 75;
        public static int KoreSsoError = 77;
        public static int GetPopupDataMethodCalled = 78;
    }
}
