﻿namespace Rewards4Platform.API.Shared.Models
{
    public class PointsStatementTransaction
    {
        public int TransactionId { get; set; }

        public int TypeId { get; set; }

        public int MemberId { get; set; }

        public int AffiliateId { get; set; }

        public int QuidcoId { get; set; }

        public long? QuidcoId_64bit { get; set; }

        public int QuidcomerchantId { get; set; }

        public int StatusId { get; set; }

        public int StateId { get; set; }

        public int ClubId { get; set; }

        public int DescriptionId { get; set; }

        public int? PartnerId { get; set; }

        public string TransactionSourceId { get; set; } = string.Empty;

        public int TransactionPoints { get; set; }

        public decimal TransactionPrice { get; set; }

        public decimal TransactionCommissionEarned { get; set; }

        public DateTime TransactionDate { get; set; }

        public DateTime TransactionInsertDate { get; set; }

        public DateTime TransactionActiveDate { get; set; }

        public bool TransactionSuppress { get; set; }

        public DateTime? TransactionSuppressedDate { get; set; }

        public DateTime? TransactionLastStateChangeDate { get; set; }

        public DateTime? TransactionLastStatusChangeDate { get; set; }

        public DateTime TransactionRecordInsertDate { get; set; }

        public int? ParentTransactionId { get; set; }

        public int? RootTransactionId { get; set; }

        public bool? TransactionRedemptionDataFixApplied { get; set; }

        public bool? TransactionLastStatusChangeDateFixApplied { get; set; }

        public int? TSourceId { get; set; }

        public int? SummaryId { get; set; }

        public int? PointsBeforeCap { get; set; }

        public int? ClubCashAmount { get; set; }

        public int? PointsAmount { get; set; }

        public int? RetailId { get; set; }

        public int? MerchantId { get; set; }

        public string DescriptionText { get; set; } = string.Empty;

        public DateTime DescriptionDate { get; set; }
    }
}
