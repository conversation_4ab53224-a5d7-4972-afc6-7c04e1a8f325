﻿namespace Rewards4Platform.API.Shared.Constants
{
    public class CacheKey
    {
        public class Merchant
        {
            public static string SuppressedMerchants(string applicationName, int clubId) => $"merchants:suppressed:{applicationName}:clubid:{clubId}:Tcb";
            public static string MerchantDetails(string urlName, string applicationName, int clubId) => $"merchant:{urlName}:{applicationName}:clubid:{clubId}";
        }
        public class PointsStatement
        {
            public static string PointsConversionRatesCacheKey() => "data:points-conversion-rates";

        }
        public class Member
        {
            public static string MemberCacheKey(int memberId) => $"member:{memberId}:member";

        }
    }
}
