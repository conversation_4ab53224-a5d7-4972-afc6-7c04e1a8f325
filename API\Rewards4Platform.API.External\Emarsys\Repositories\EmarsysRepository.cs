﻿using Dapper;
using Rewards4Platform.API.Data.Membership;
using Rewards4Platform.API.External.Emarsys.Models;
using Rewards4Platform.API.Shared.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.External.Emarsys.Repositories
{
    public interface IEmarsysRepository
    {
        Task<EmarsysSettingDbSet?> GetEmarsysSetting(string applicationName);
    }

    public class EmarsysRepository : IEmarsysRepository
    {
        private readonly IMembershipDbSqlConnectionFactory _membershipDbConnectionFactory;

        public EmarsysRepository(IMembershipDbSqlConnectionFactory membershipDbConnectionFactory)
        {
            _membershipDbConnectionFactory = membershipDbConnectionFactory;
        }

        public async Task<EmarsysSettingDbSet?> GetEmarsysSetting(string applicationName)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = "usp_Website_GetEmarsysSetting";

            var parameters = new
            {
                ApplicationName = applicationName
            };

            var emarsysSetting = await sqlConnection.QueryFirstOrDefaultAsync<EmarsysSettingDbSet?>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return emarsysSetting;
        }
    }
}
