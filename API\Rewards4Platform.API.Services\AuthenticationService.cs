﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Identity.Client;
using Rewards4Platform.API.Data.Clubs.Repositories;
using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Extensions;
using Rewards4Platform.API.Shared.Configurations;
using Rewards4Platform.API.Shared.Enums;
using Rewards4Platform.API.Shared.Models;
using Rewards4Platform.API.Shared.Requests;
using Rewards4Platform.API.Shared.Responses;
using Rewards4Platform.Common.Helpers;
using System;
using System.Net;

namespace Rewards4Platform.API.Services
{
    public interface IAuthenticationService
    {        
        Task<LoginMemberResponse> Login(LoginRequest request, HttpRequest httpRequest);
        void Logout();
        Task<RegisterMemberResponse> Register(RegistrationRequest registrationRequest, HttpRequest httpRequest);
        Task<SendResetPasswordEmailResponse> SendResetPasswordEmail(SendResetPasswordEmailRequest request, HttpRequest httpRequest);
        Task<ValidateResetPasswordCodeResponse> ValidateResetPasswordCode(ValidateResetPasswordCodeRequest validateResetPasswordCodeRequest);
        Task<ResetPasswordResponse> ResetPassword(ResetPasswordRequest resetPasswordRequest);
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly ITenantService _tenantService;
        private readonly IHashingService _hashingService;
        private readonly IJwtProvider _jwtProvider;
        private readonly IMemberService _memberService;
        private readonly IAuthenticationRepository _authenticationRepository;
        private readonly IClubService _clubService;
        private readonly IApplicationService _applicationService;
        private readonly ILoggingService _loggingService;
        private readonly IEmailService _emailService;

        public AuthenticationService(ITenantService tenantService,
                                     IHashingService hashingService,
                                     IJwtProvider jwtProvider,
                                     IMemberService memberService,
                                     IAuthenticationRepository authenticationRepository,
                                     IClubService clubService,
                                     IApplicationService applicationService,
                                     ILoggingService loggingService,
                                     IEmailService emailService)
        {
            _tenantService = tenantService;
            _hashingService = hashingService;
            _jwtProvider = jwtProvider;
            _memberService = memberService;
            _authenticationRepository = authenticationRepository;
            _clubService = clubService;
            _applicationService = applicationService;
            _loggingService = loggingService;
            _emailService = emailService;
        }

        public async Task<LoginMemberResponse> Login(LoginRequest request, HttpRequest httpRequest)
        {
            var response = new LoginMemberResponse();
            var tenant = _tenantService.GetCurrentTenant();
            var applicationName = tenant.TenantConfiguration.ApplicationName;
            var hashedEmail = _hashingService.HashEmail(request.EmailAddress);

            // check if account exists and account is not locked out
            var validateLoginResult = await _authenticationRepository.ValidateLogin(applicationName, hashedEmail);

            if (!validateLoginResult.AccountExists)
            {
                return new LoginMemberResponse
                {
                    IsSuccess = false,
                    Message = "Member does not exist.",
                    AccountExists = false,
                    IsLockedOut = false,
                    InvalidCredentials = true,
                    MemberId = 0,
                    Token = string.Empty,
                    IsDeepLinkLogin = false,
                    RedirectUrl = string.Empty
                };
            }

            if (validateLoginResult.AccountLocked)
            {
                return new LoginMemberResponse
                {
                    IsSuccess = false,
                    Message = "Account is locked.",
                    AccountExists = true,
                    IsLockedOut = true,
                    InvalidCredentials = true,
                    MemberId = 0,
                    Token = string.Empty,
                    IsDeepLinkLogin = false,
                    RedirectUrl = string.Empty
                };
            }

            // Validate password
            var isPasswordValid = await VerifyPassword(validateLoginResult, request.Password);

            if (isPasswordValid)
            {
                // update successful login attempts
                await _authenticationRepository.UpdateMemberInfoAfterLoginAttempt(
                    (Guid)validateLoginResult.ApplicationId!,
                    (Guid)validateLoginResult.UserId!,
                    true).ConfigureAwait(false);

                // Generate JWT token
                var memberClaim = new MemberClaim
                {
                    MemberId = validateLoginResult.MemberId,
                    Email = validateLoginResult.UsernameEmail!,
                    Name = validateLoginResult.Forename!,
                    IsDeepLinkLogin = false
                };

                var token = await _jwtProvider.GenerateToken(memberClaim);

                // Set response properties
                response.IsSuccess = true;
                response.Message = "Login successful.";
                response.AccountExists = true;
                response.IsLockedOut = false;
                response.InvalidCredentials = false;
                response.MemberId = validateLoginResult.MemberId;
                response.Token = token;
                response.IsDeepLinkLogin = false;

                // set redirect URL
                if (!string.IsNullOrEmpty(request.RedirectUrl))
                {
                    var decodedUrl = WebUtility.UrlDecode(request.RedirectUrl);

                    var returnUrlStartIndex = decodedUrl.IndexOf("returnurl=");
                    if (returnUrlStartIndex >= 0)
                    {
                        var returnurl = decodedUrl.Substring(returnUrlStartIndex + "returnurl=".Length);
                        response.RedirectUrl = returnurl;
                    }
                }

                return response;
            }
            else
            {
                // update the failed login attempts
                await _authenticationRepository.UpdateMemberInfoAfterLoginAttempt(
                    (Guid)validateLoginResult.ApplicationId!,
                    (Guid)validateLoginResult.UserId!,
                    false).ConfigureAwait(false);

                return new LoginMemberResponse
                {
                    IsSuccess = false,
                    Message = "Invalid Credentials",
                    AccountExists = true,
                    IsLockedOut = false,
                    InvalidCredentials = true,
                    MemberId = 0,
                    Token = string.Empty,
                    IsDeepLinkLogin = false,
                    RedirectUrl = string.Empty
                };
            }
        }

        public async Task<RegisterMemberResponse> Register(RegistrationRequest registrationRequest, HttpRequest httpRequest)
        {
            var remoteClient = httpRequest.GetRemoteClientDetails();
            var tenant = _tenantService.GetCurrentTenant();
            var siteConfiguration = tenant.TenantConfiguration;
            var applicationName = siteConfiguration.ApplicationName;
            var hashedEmail = _hashingService.HashEmail(registrationRequest.Email);

            // Check if the email already exists
            var memberId = await _memberService.GetMemberIdByApplicationNameAndEmail(applicationName, hashedEmail);

            if (memberId > 0)
            {
                var response = new RegisterMemberResponse();

                var isRacingIrelandMember = false;

                if (httpRequest.IsRacingIrelandSite())
                {
                    isRacingIrelandMember = await _memberService.IsRacingIrelandMember(memberId);
                }

                return new RegisterMemberResponse
                {
                    IsSuccess = false,
                    Message = "User already exists.",
                    MemberId = null,
                    Token = string.Empty,
                    IsDeepLinkLogin = false,
                    AccountExists = true,
                    IsRacingIrelandMember = isRacingIrelandMember
                };
                //**** Things to do

                // Add the ireland popup
                // CMS part add later singleusecode
                // get remote client details for loging IP address
                // Add vaidation for passowrd conplexity check the Rewards site
                // Validate Name and email address
                // Log registration of user
                // Log users accepted terms and condition
                // set clubid for user pass in procedure

                //****

                // TODO: Handle the case where the member is already registered with a club

                //var memberClubId = _clubService.GetMemberClubId(memberId);

                //if (SiteConfiguration.OldClubId <= 0)
                //    return;

                //if (memberClubId == SiteConfiguration.OldClubId)
                //    ShowPopup = true;

                //if (memberClubId == 0 && SiteConfiguration.OldClubId > 0)
                //    ShowPopup = true;
            }

            // Create a new member           
            var userName = Guid.NewGuid();
            var encryptedEmail = _hashingService.Encrypt(registrationRequest.Email, userName.ToString());
            var passwordSalt = _hashingService.GeneratePasswordSalt();
            var hashedPassword = _hashingService.HashPassword(registrationRequest.Password, passwordSalt);
            var applicationId = await _applicationService.GetApplicationId(applicationName);

            var createAccountRequest = new NewAccountDetails
            {
                UserName = userName.ToString(),
                LoweredUserName = userName.ToString().ToLower(),
                UsernameLogin = userName,
                UsernameEmail = hashedEmail,
                MemberReferralMemberId = registrationRequest.MemberReferralId ?? 0,
                MemberSourceId = httpRequest.GetSourceIdFromUrl(),
                MemberForename = registrationRequest.Name,
                ApplicationId = applicationId,
                ApplicationName = applicationName,
                Password = hashedPassword,
                PasswordSalt = passwordSalt,
                Email = encryptedEmail,
                LoweredEmail = encryptedEmail.ToLower(),
                ClubId = siteConfiguration.ClubId
            };

            var newMemberId = await _authenticationRepository.CreateAccount(createAccountRequest);

            if (newMemberId > 0)
            {
                if (siteConfiguration.IsClubSite)
                {
                    await _clubService.InsertClubMember(newMemberId, createAccountRequest.ClubId).ConfigureAwait(false);
                }

                // Generate JWT token
                var memberClaim = new MemberClaim
                {
                    MemberId = newMemberId,
                    Email = createAccountRequest.UsernameEmail,
                    Name = createAccountRequest.MemberForename,
                    IsDeepLinkLogin = false
                };

                var token = await _jwtProvider.GenerateToken(memberClaim);

                return new RegisterMemberResponse
                {
                    IsSuccess = true,
                    Message = "User registered successfully.",
                    MemberId = newMemberId,
                    Token = token,
                    IsDeepLinkLogin = false,
                    AccountExists = false
                };
            }

            return new RegisterMemberResponse
            {
                IsSuccess = false,
                Message = "User registration failed.",
                MemberId = null,
                Token = string.Empty,
                IsDeepLinkLogin = false,
                AccountExists = false
            };
        }

        private async Task<bool> VerifyPassword(ValidateMemberLoginDbSet validateLoginResult, string plainPassword)
        {
            var passwordFromDb = await _authenticationRepository.GetMemberAccountPassword((Guid)validateLoginResult.ApplicationId!, (Guid)validateLoginResult.UserId!).ConfigureAwait(false);
            if (passwordFromDb is null)
            {
                return false;
            }

            var hashedPassword = _hashingService.HashPassword(plainPassword, passwordFromDb.PasswordSalt);

            return hashedPassword == passwordFromDb.Password;
        }

        public void Logout()
        {
            throw new NotImplementedException();
        }

        public async Task<SendResetPasswordEmailResponse> SendResetPasswordEmail(SendResetPasswordEmailRequest request, HttpRequest httpRequest)
        {
            var tenant = _tenantService.GetCurrentTenant();
            var applicationName = tenant.TenantConfiguration.ApplicationName;
            var emarSysEventPrefix = tenant.TenantConfiguration.EmarsysEventPrefix;
            var hostName = httpRequest.HttpContext.Request.Scheme + "://" + httpRequest.HttpContext.Request.Host;

            var hashedEmail = _hashingService.HashEmail(request.Email);

            var member = await _memberService.GetUsernameByApplicationNameAndEmail(applicationName, hashedEmail).ConfigureAwait(false);

            if (member is null)
            {
                return new SendResetPasswordEmailResponse
                {
                    IsSuccess = false,
                    Message = "Member not found."
                };
            }

            var code = RandomString.Generate(70, true);
            code = code.Replace(" ", "").Replace("%20", "");

            var createResetPasswordKey = true;
            var createResetPasswordKeyResult = await _authenticationRepository.CreateOrValidateResetPasswordKey(createResetPasswordKey, member.MemberId, member.UserId, code).ConfigureAwait(false);

            if (createResetPasswordKeyResult is null)
            {
                return new SendResetPasswordEmailResponse
                {
                    IsSuccess = false,
                    Message = "Failed to create reset password key."
                };
            }

            // Send email to user with reset password link
            var passwordResetLink = $"{hostName}/reset-password?mid={member.MemberId}&gid={member.UserId}&code={code}";

            var placeholderReplacements = new Dictionary<string, object>()
            {
                {"RESETLINK", passwordResetLink},
                {"FIRSTNAME", createResetPasswordKeyResult.FirstName},
            };
            
            var isEmailSent = await _emailService.Send(applicationName, member.MemberId, emarSysEventPrefix, EmailEvent.ForgotPassword, placeholderReplacements).ConfigureAwait(false);

            if (!isEmailSent)
            {
                return new SendResetPasswordEmailResponse
                {
                    IsSuccess = false,
                    Message = "Failed to send reset password email."
                };
            }

            return new SendResetPasswordEmailResponse
            {
                IsSuccess = true,
                Message = "Password reset email sent successfully"
            };
        }

        public async Task<ValidateResetPasswordCodeResponse> ValidateResetPasswordCode(ValidateResetPasswordCodeRequest validateResetPasswordCodeRequest)
        {
            var isValid = await _authenticationRepository.ValidateResetPasswordCode(validateResetPasswordCodeRequest.MemberId, validateResetPasswordCodeRequest.UserId, validateResetPasswordCodeRequest.Code).ConfigureAwait(false);

            if (isValid)
            {
                return new ValidateResetPasswordCodeResponse
                {
                    IsSuccess = true,
                    Message = "Valid code."
                };
            }

            return new ValidateResetPasswordCodeResponse
            {
                IsSuccess = false,
                Message = "Invalid code."
            };
        }

        public async Task<ResetPasswordResponse> ResetPassword(ResetPasswordRequest resetPasswordRequest)
        {
            throw new NotImplementedException();
        }
    }
}
