﻿using FluentValidation;
using Rewards4Platform.API.Shared.Requests;

namespace Rewards4Platform.API.Validators
{
    public class ResetPasswordRequestValidator : AbstractValidator<ResetPasswordRequest>
    {
        public ResetPasswordRequestValidator()
        {
            RuleFor(x => x.Password)
               .NotEmpty().WithMessage("Password is required.")
               .MinimumLength(8).WithMessage("Password must be at least 8 characters long.")
               .MaximumLength(64).WithMessage("Password cannot exceed 64 characters.")
               .Must(ValidatorMethods.IsValidPassword);

            RuleFor(x => x.ValidationCode)
                .NotEmpty()
                .WithMessage("Invalid reset password code.");
        }
    }
}
