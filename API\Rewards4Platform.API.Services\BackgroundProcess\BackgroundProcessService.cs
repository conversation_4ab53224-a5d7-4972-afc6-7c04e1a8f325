﻿using Newtonsoft.Json;
using Rewards4Platform.API.Services.HttpClientServices;
using Rewards4Platform.API.Shared.Models;
using System.Net;
using Exceptionless;

namespace Rewards4Platform.API.Services.BackgroundProcess
{
    public interface IBackgroundProcessService
    {
        Task CreateLog(int typeId, int memberId, string note, int merchantId, Guid? customMerchantId, RemoteClientInformation client);
    }

    public class BackgroundProcessService : IBackgroundProcessService
    {
        private readonly IBackgroundProcessApiHttpClientService _backgroundProcessApiHttpClientService;

        public BackgroundProcessService(IBackgroundProcessApiHttpClientService backgroundProcessApiHttpClientService)
        {
            _backgroundProcessApiHttpClientService = backgroundProcessApiHttpClientService;
        }

        public async Task CreateLog(int typeId, int memberId, string note, int merchantId, Guid? customMerchantId, RemoteClientInformation client)
        {
            try
            {
                var url = "/api/create-log";

                var payload = JsonConvert.SerializeObject(new LogInfo
                {
                    TypeId = typeId,
                    MemberId = memberId,
                    Note = note,
                    MerchantId = merchantId,
                    CustomMerchantId = customMerchantId,
                    Date = DateTime.Now,
                    IpAddress = client?.IpAddress,
                    UserAgent = client?.UserAgent,
                });

                var response = await _backgroundProcessApiHttpClientService.ExecutePostAsync(url, payload).ConfigureAwait(false);

                if (response.StatusCode is HttpStatusCode.Created)
                {
                    await Task.CompletedTask;
                }
                else
                {
                    ExceptionlessClient.Default.SubmitLog(typeof(BackgroundProcessService).FullName!, $"Invalid response from Background Process API. Status Code: ${response.StatusCode} Reason: ${response.ReasonPhrase}");
                }
            }
            catch (Exception ex)
            {
                ExceptionlessClient.Default.SubmitLog(typeof(BackgroundProcessService).FullName!, $"Could not connect to Background Process API. Error: {ex.InnerException.Message}");
            }
        }
    }
}
