﻿using System.Text;

namespace Rewards4Platform.API.Services.HttpClientServices
{
    public interface IBackgroundProcessApiHttpClientService
    {
        Task<HttpResponseMessage> ExecutePostAsync(string url, string body);
    }

    public class BackgroundProcessApiHttpClientService : IBackgroundProcessApiHttpClientService
    {
        private readonly HttpClient _httpClient;

        public BackgroundProcessApiHttpClientService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }        

        public async Task<HttpResponseMessage> ExecutePostAsync(string url, string body)
        {
            var requestContent = new StringContent(body, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(url, requestContent);
            return response;
        }
    }
}
