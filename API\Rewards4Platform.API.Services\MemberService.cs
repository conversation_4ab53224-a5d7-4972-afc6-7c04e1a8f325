﻿using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Shared.Constants;
using Rewards4Platform.API.Shared.Mappers;
using Rewards4Platform.API.Shared.Models;
using Rewards4Platform.API.Shared.Requests;
using Rewards4Platform.API.Shared.Responses;
using StackExchange.Redis;
using StackExchange.Redis.Extensions.Core.Abstractions;
using System;

namespace Rewards4Platform.API.Services
{
    public interface IMemberService
    {
        Task<MemberResponse> GetMemberAsync(int memberId, bool siteUsesClubCredit = false);
        Task<int> GetMemberIdByApplicationNameAndEmail(string applicationName, string hashedEmail);
        Task<UserNameDbSet?> GetUsernameByApplicationNameAndEmail(string applicationName, string hashedEmail);
        Task<bool> IsRacingIrelandMember(int memberId);
        Task<MemberDetailsResponse?> GetMemberDetailsAsync(string applicationName, int memberId);
        Task<UpdateMemberResponse> UpdateMemberDetails(int memberId, UpdateMemberRequest updateMemberRequest);
    }

    public class MemberService : IMemberService
    {
        private readonly IMemberRepository _memberRepository;
        private readonly IHashingService _hashingService;
        private readonly IRedisDatabase _redis;

        public MemberService(IMemberRepository memberRepository, IHashingService hashingService, IRedisDatabase redis)
        {
            _memberRepository = memberRepository;
            _hashingService = hashingService;
            _redis = redis;
        }

        public async Task<MemberResponse> GetMemberAsync(int memberId, bool siteUsesClubCredit = false)
        {
            //WARNING if you update this method you must do the same in the transaction service           

            var cacheKey = CacheKey.Member.MemberCacheKey(memberId);

            var member = await _redis.GetAsync<MemberResponse>(cacheKey);

            if (member != null)
            {
                return member;
            }

            var memberFromDb = await _memberRepository.GetMember(memberId, siteUsesClubCredit);
            if (memberFromDb is not null)
            {
                member = MemberMapper.Map(memberId, memberFromDb);

                await _redis.AddAsync(cacheKey, member, CacheDuration.Member.MemberDetails);
            }

            return member;
        }

        /// <summary>
        /// Get member id by application name and hashed email.
        /// </summary>
        /// <returns> 0 if member not found otherwise returns memberId</returns>
        public async Task<int> GetMemberIdByApplicationNameAndEmail(string applicationName, string hashedEmail)
        {
            var memberId = await _memberRepository.GetMemberIdByApplicationNameAndEmail(applicationName, hashedEmail);

            return memberId;
        }

        /// <summary>
        /// Get info from aspnet_usernames table by application name and hashed email.
        /// </summary>
        /// <returns>Username info</returns>
        public async Task<UserNameDbSet?> GetUsernameByApplicationNameAndEmail(string applicationName, string hashedEmail)
        {
            var usernameInfo = await _memberRepository.GetUsernameByApplicationNameAndEmail(applicationName, hashedEmail).ConfigureAwait(false);

            return usernameInfo;
        }

        public async Task<bool> IsRacingIrelandMember(int memberId)
        {
            var isRacingIrelandMember = await _memberRepository.IsRacingIrelandMember(memberId).ConfigureAwait(false);

            return isRacingIrelandMember;
        }

        public async Task<MemberDetailsResponse?> GetMemberDetailsAsync(string applicationName, int memberId)
        {
            var cacheKey = CacheKey.Member.MemberCacheKey(memberId);

            var member = await _redis.GetAsync<MemberDetailsResponse>(cacheKey);

            if (member != null)
            {
                return member;
            }

            var memberFromDb = await _memberRepository.GetMemberDetails(applicationName, memberId);

            if (memberFromDb is not null)
            {
                member = MemberMapper.Mappper(memberId, memberFromDb);
                member.EmailAddress = await _hashingService.DecryptEmail(memberFromDb.EmailAddress, memberFromDb.UserName);

                await _redis.AddAsync(cacheKey, member, CacheDuration.Member.MemberDetails);
            }

            return member;
        }

        public async Task<UpdateMemberResponse> UpdateMemberDetails(int memberId, UpdateMemberRequest updateMemberRequest)
        {
            var updatedMember = await _memberRepository.UpdateMemberDetails(memberId, updateMemberRequest).ConfigureAwait(false);

            if(updatedMember is null)
            {
                return new UpdateMemberResponse
                {
                    IsSuccess = false,
                    Message = "Update member details failed."
                };
            }

            return new UpdateMemberResponse
            {
                IsSuccess = true,
                Message = "Member details updated successfully.",
                UpdatedMember = updatedMember
            };
        }
    }
}