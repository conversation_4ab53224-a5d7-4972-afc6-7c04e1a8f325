﻿namespace Rewards4Platform.API.Shared.Models
{
    public class UserNameDbSet
    {
        public int UsernameId { get; set; }
        public string ApplicationName { get; set; } = string.Empty;
        public int MemberId { get; set; }
        public Guid UserId { get; set; }
        public Guid UsernameLogin { get; set; }
        public string UsernameEmail { get; set; } = string.Empty;
        public string UsernameMobile { get; set; } = string.Empty;
        public DateTime UsernameDate { get; set; }
        public bool UsernameSuppress { get; set; }
    }
}
