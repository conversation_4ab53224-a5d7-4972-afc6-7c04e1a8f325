﻿using FluentValidation;
using Rewards4Platform.API.Shared.Requests;
using System.Text.RegularExpressions;

namespace Rewards4Platform.API.Validators
{
    public sealed class RegistrationRequestValidator : AbstractValidator<RegistrationRequest>
    {
        public RegistrationRequestValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Name is required.")
                .MinimumLength(2).WithMessage("Name must be at least 2 characters long.")
                .MaximumLength(25).WithMessage("Name cannot exceed 25 characters.");

            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Email is required.")
                .EmailAddress().WithMessage("Invalid email address format.")
                .MinimumLength(6).WithMessage("Email must be at least 6 characters long.")
                .MaximumLength(50).WithMessage("Email cannot exceed 50 characters.");

            RuleFor(x => x.Password)
                .NotEmpty().WithMessage("Password is required.")
                .MinimumLength(8).WithMessage("Password must be at least 8 characters long.")
                .MaximumLength(50).WithMessage("Password cannot exceed 64 characters.")
                .Must(ValidatorMethods.IsValidPassword);
        }
    }        
}
