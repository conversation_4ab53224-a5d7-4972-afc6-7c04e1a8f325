﻿using Rewards4Platform.API.Services;

namespace Rewards4Platform.API.Middlewares
{
    public class TenantResolutionMiddleware
    {
        private readonly RequestDelegate _next;

        // Paths that don't require tenant headers
        private static readonly HashSet<string> ExcludedPaths = new(StringComparer.OrdinalIgnoreCase)
        {
            "/health",
            "/swagger",
            "/api/health"
        };

        public TenantResolutionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLowerInvariant();

            // Skip tenant validation for excluded paths
            if (ShouldSkipTenantValidation(path))
            {
                await _next(context);
                return;
            }

            var tenantService = context.RequestServices.GetRequiredService<ITenantService>();

            var tenant = tenantService.GetCurrentTenant();

            // Store tenant context for the entire request pipeline
            context.Items["TenantContext"] = tenant;

            await _next(context);            
        }

        private static bool ShouldSkipTenantValidation(string? path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return false;
            }

            // Check exact matches and prefix matches
            return ExcludedPaths.Any(excludedPath =>
                path.Equals(excludedPath, StringComparison.OrdinalIgnoreCase) ||
                path.StartsWith(excludedPath + "/", StringComparison.OrdinalIgnoreCase));
        }
    }
}
