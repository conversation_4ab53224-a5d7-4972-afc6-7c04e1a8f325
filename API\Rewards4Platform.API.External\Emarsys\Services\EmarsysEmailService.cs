﻿using Exceptionless;
using Newtonsoft.Json;
using Rewards4Platform.API.External.Emarsys.Models;
using Rewards4Platform.API.External.Emarsys.Repositories;
using Rewards4Platform.API.External.Emarsys.Responses;
using Rewards4Platform.API.Services;
using Rewards4Platform.API.Shared.Enums;
using Rewards4Platform.API.Shared.Extensions;
using Rewards4Platform.API.Shared.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.External.Emarsys.Services
{
    public class EmarsysEmailService : IEmailService
    {
        private readonly IEmarsysRepository _emarsysRepository;
        private readonly IEmarsysHttpClientService  _emarsysHttpClientService;
        private string? _memberIdFieldId = string.Empty;

        public EmarsysEmailService(IEmarsysRepository emarsysRepository, IEmarsysHttpClientService emarsysHttpClientService)
        {
            _emarsysRepository = emarsysRepository;
            _emarsysHttpClientService = emarsysHttpClientService;
        }

        public async Task<bool> Send(string applicationName, int memberId, string emarsysPrefix, EmailEvent eventName, Dictionary<string, object> placeholderReplacements)
        {
            var emarsysEventName = BuildEventName(eventName, emarsysPrefix);
            var credentials = await _emarsysRepository.GetEmarsysSetting(applicationName);

            if (credentials is null)
            {
                return false;
            }

            await SetEmarsysFields(credentials);

            var body = GenerateJsonString(memberId.ToString(), _memberIdFieldId!, placeholderReplacements);

            var events = await _emarsysHttpClientService.Send<EmarsysFieldResponse>("GET", "event");

            var eventId = events.Data.FirstOrDefault(x => x.Name == emarsysEventName)?.Id;

            var url = $"event/{eventId}/trigger";

            EmarsysResponse result;            
            
            result = await _emarsysHttpClientService.Send<EmarsysResponse>("POST", url, body);
            if (result.ReplyText.ToLower() != "ok")
            {
                ExceptionlessClient.Default.SubmitLog(typeof(EmarsysEmailService).FullName!,
                    $"Could not send email with event {emarsysEventName}. Reason is {result.ToJson()}", "Error");
            }
            
            return true;
        }

        private string BuildEventName(EmailEvent name, string emarsysPrefix)
        {
            switch (name)
            {
                case EmailEvent.ForgotPassword:
                    return $"Rewards4Sport - {emarsysPrefix} - Forgot password event";
                case EmailEvent.ContactUs:
                    return $"Rewards4Sport - {emarsysPrefix} - Contact us event";
                default:
                    throw new ArgumentOutOfRangeException(nameof(name), name,
                        $"Cannot send email as email event name {name} is not set up!");
            }
        }

        private async Task SetEmarsysFields(EmarsysSettingDbSet emarsysSettings)
        {
            var fields = await _emarsysHttpClientService.Send<EmarsysFieldResponse>("GET", "field/translate/en");

            _memberIdFieldId = fields.Data.FirstOrDefault(m => m.Name == emarsysSettings.EmarsysUid)?.Id;

            if (_memberIdFieldId == null)
            {
                throw new Exception("_memberIdFieldId is null");
            }
        }

        /// <summary>
        /// This method generates a json string based on incoming arguments
        /// </summary>
        /// <param name="memberId"></param>
        /// <param name="memberFieldId"></param>
        /// <param name="placeholders"></param>
        /// <returns>Json string</returns>
        private string GenerateJsonString(string memberId, string memberFieldId, Dictionary<string, object> placeholders)
        {
            string GetValueOrDefault(string key) => placeholders.TryGetValue(key, out var value) ? value?.ToString() ?? string.Empty : string.Empty;

            object GetJsonObjectOrDefault(string key)
            {
                if (placeholders.TryGetValue(key, out var value) && value is string jsonString)
                {
                    try
                    {
                        return JsonConvert.DeserializeObject(jsonString) ?? string.Empty;
                    }
                    catch
                    {
                        return jsonString;
                    }
                }

                return string.Empty;
            }

            var data = new
            {
                global = new
                {
                    RESETLINK = GetJsonObjectOrDefault("RESETLINK"),
                    FIRSTNAME = GetValueOrDefault("FIRSTNAME"),
                }
            };

            var jsonObject = new
            {
                key_id = memberFieldId,
                external_id = memberId,
                data
            };

            var jsonSettings = new JsonSerializerSettings
            {
                StringEscapeHandling = StringEscapeHandling.EscapeNonAscii
            };

            var json = JsonConvert.SerializeObject(jsonObject, jsonSettings);
            var encodedJson = Encoding.UTF8.GetBytes(json);

            return Encoding.UTF8.GetString(encodedJson);
        }
    }
}
