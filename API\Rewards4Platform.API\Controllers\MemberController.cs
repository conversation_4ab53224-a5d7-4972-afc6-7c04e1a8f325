﻿using Azure.Core;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rewards4Platform.API.Extensions;
using Rewards4Platform.API.Services;
using Rewards4Platform.API.Shared.Requests;
using Rewards4Platform.API.Shared.Responses;
using Rewards4Platform.API.Validators;
using System.ComponentModel.DataAnnotations;

namespace Rewards4Platform.API.Controllers
{
    [Route("api/member")]
    [ApiController]
    public class MemberController : ControllerBase
    {
        private readonly ITenantService _tenantService;
        private readonly IPointsStatementService _pointsStatementService;
        private readonly IMemberService _memberService;
        private readonly IJwtProvider _jwtProvider;
        public MemberController(ITenantService tenantService,IPointsStatementService pointsStatementService, IMemberService memberService, IJwtProvider jwtProvider)
        {
            _tenantService = tenantService;
            _pointsStatementService = pointsStatementService;
            _memberService = memberService;
            _jwtProvider = jwtProvider;
        }
        
        [HttpGet]
        [Route("points-statement")]
        [Authorize]
        public async Task<IActionResult> GetMemberPointsStatement()
        {
            var userInfoFromToken = _jwtProvider.GetMemberInfoFromToken(Request);
            if (userInfoFromToken is null)
            {
                return Unauthorized();
            }

            var tenant = _tenantService.GetCurrentTenant();

            var pointsStatement = await _pointsStatementService.GetPointsStatementAsync(
                 tenant.TenantConfiguration.ApplicationDisplayName,
                 tenant.TenantConfiguration.ApplicationName,
                 userInfoFromToken.MemberId,
                 tenant.TenantConfiguration.IsUsingClubCredit,
                 true, 
                 tenant.TenantConfiguration.IsUsingCash,
                 0, 25);

            var member = await _memberService.GetMemberAsync(userInfoFromToken.MemberId, tenant.TenantConfiguration.IsUsingClubCredit);

            if (pointsStatement.First().Status != null && pointsStatement.First().Status.Equals("default"))
            {
                pointsStatement.First().Description = pointsStatement.First().Description.Replace("{{name}}", member.Forename);
            }

            return Ok(pointsStatement);
        }

        [HttpGet]
        [Route("details")]
        [Authorize]
        public async Task<IActionResult> GetMemberDetails()
        {
            var userInfoFromToken = _jwtProvider.GetMemberInfoFromToken(Request);
            if (userInfoFromToken is null)
            {
                return Unauthorized();
            }

            var tenant = _tenantService.GetCurrentTenant();
            var applicationName = tenant.TenantConfiguration.ApplicationName;
            var memberInfo = await _memberService.GetMemberDetailsAsync(applicationName, userInfoFromToken.MemberId);            
            
            return Ok(memberInfo);
        }

        [HttpPut]
        [Route("update")]
        [Authorize]
        public async Task<IActionResult> UpdateMemberDetails([FromBody] UpdateMemberRequest updateMemberRequest, IValidator<UpdateMemberRequest> validator)
        {
            var userInfoFromToken = _jwtProvider.GetMemberInfoFromToken(Request);
            if (userInfoFromToken is null)
            {
                return Unauthorized();
            }

            validator.ValidateAndThrow(updateMemberRequest);

            var updateMemberResponse = await _memberService.UpdateMemberDetails(userInfoFromToken.MemberId, updateMemberRequest).ConfigureAwait(false);

            return Ok(updateMemberResponse);
        }
    }
}
