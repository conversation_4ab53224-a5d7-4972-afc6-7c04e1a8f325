﻿using Rewards4Platform.API.Shared.Models;
using Rewards4Platform.API.Shared.Responses;
using System.Globalization;

namespace Rewards4Platform.API.Shared.Mappers
{
    public class MemberMapper
    {
        public static MemberResponse Map(int memberId, MemberDbSet memberFromDb)
        {
            var cultureInfo = new CultureInfo("en-GB");
            var dateofBirth = DateTime.Parse(memberFromDb.DateOfBirth.ToString("D"), cultureInfo);

            return new MemberResponse()
            {
                MemberId = memberId,
                Application = memberFromDb.Application,
                Forename = memberFromDb.Forename,
                Surname = memberFromDb.Surname,
                Address = memberFromDb.Address,
                AffiliateId = memberFromDb.AffiliateId,
                EmailVerification = memberFromDb.EmailVerification,
                PointsAvailable = memberFromDb.PointsAvailable,
                PointsAvailableText = memberFromDb.PointsAvailableText,
                QuidcoId = memberFromDb.QuidcoId,
                UserId = memberFromDb.UserId,
                Postcode = memberFromDb.Postcode,
                DateOfBirth = dateofBirth,
                GenderId = memberFromDb.GenderId,
                MobileNumber = memberFromDb.MobileNumber,
                AccountCreatedDate = memberFromDb.AccountCreatedDate,
                HasClubCreditOnAccount = memberFromDb.MemberHasClubCredit,
                ClubCreditPoints = memberFromDb.ClubCreditAvailablePoints,
                ClubCreditPointsValue = memberFromDb.ClubCreditAvailableText,
                TotalAvailableToSpendValue = memberFromDb.TotalAvailableSpend
            };
        }

        public static MemberDetailsResponse Mappper(int memberId, MemberDbSet memberFromDb)
        {
            var dateofBirth = memberFromDb.DateOfBirth.ToString("dd/MM/yyyy");

            return new MemberDetailsResponse()
            {
                MemberId = memberId,
                Forename = memberFromDb.Forename,
                Surname = memberFromDb.Surname,
                Address = memberFromDb.Address,
                Postcode = memberFromDb.Postcode,
                DateOfBirth = dateofBirth,
                MobileNumber = memberFromDb.MobileNumber
            };
        }
    }
}
