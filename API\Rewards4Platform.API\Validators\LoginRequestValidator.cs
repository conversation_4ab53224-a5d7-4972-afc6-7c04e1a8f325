﻿using FluentValidation;
using Rewards4Platform.API.Shared.Requests;
using System.Text.RegularExpressions;

namespace Rewards4Platform.API.Validators
{
    public sealed class LoginRequestValidator : AbstractValidator<LoginRequest>
    {
        public LoginRequestValidator()
        {
            RuleFor(x => x.EmailAddress)
                .NotEmpty().WithMessage("Email address is required.")
                .EmailAddress().WithMessage("Invalid email address format.")
                .MaximumLength(320).WithMessage("Email address cannot exceed 320 characters");

            RuleFor(x => x.Password)
                .NotEmpty().WithMessage("Password is required.")
                .MinimumLength(8).WithMessage("Password must be at least 8 characters long.")
                .MaximumLength(64).WithMessage("Password cannot exceed 64 characters.")
                .Must(ValidatorMethods.IsValidPassword);
        }        
    }
}
