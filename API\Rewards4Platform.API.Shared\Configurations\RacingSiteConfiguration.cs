﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.Shared.Configurations
{
    public sealed class RacingSiteConfiguration : ISiteConfiguration
    {
        public string ApplicationUrl { get; } = "racing";
        public string ApplicationDisplayName { get; } = "Rewards4Racing";
        public string ApplicationName { get; } = "Rewards4Racing";
        public bool IsClubSite { get; } = false;
        public int ClubId { get; } = 0;
        public int OldClubId { get; } = 0;
        public string TeamName { get; } = string.Empty;
        public bool IsUsingCash { get; } = false;
        public string CashDescription { get; } = string.Empty;
        public bool IsUsingClubCredit { get; } = false;
        public string EmarsysEventPrefix { get; } = "Rewards4Racing";
    }    
}
