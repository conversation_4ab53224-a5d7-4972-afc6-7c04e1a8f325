﻿using Rewards4Platform.API.Data.Membership.Repositories;

namespace Rewards4Platform.API.Services
{
    public interface IApplicationService
    {
        Task<Guid> GetApplicationId(string applicationName);
    }

    public class ApplicationService : IApplicationService
    {
        private readonly IApplicationRepository _applicationRepository;

        public ApplicationService(IApplicationRepository applicationRepository)
        {
            _applicationRepository = applicationRepository;
        }

        public async Task<Guid> GetApplicationId(string applicationName)
        {
            var applicationId = await _applicationRepository.GetApplicationId(applicationName);

            return applicationId;
        }
    }
}
