﻿using Dapper;
using Microsoft.Data.SqlClient;
using Rewards4Platform.API.Data.Membership;
using Rewards4Platform.API.Shared.Enums;
using Rewards4Platform.API.Shared.Models;
using System.Data;

namespace Rewards4Platform.API.Data.Membership.Repositories
{
    public interface IPointsStatementRepository
    {
        Task<List<PointsStatementTransaction>> GetPointsStatementTransactions(int memberId, int stateLive, int stateDeclined, int summaryId);
        Task<bool> DoesMemberHaveClubCreditTransactions(int memberId);
        Task<List<PointsConversionRates>> GetPointsConversionRates();
        Task<List<CompetitionExpiryFreeMomentEntry>> GetFreeEntries(int memberId);
    }
    
    public class PointsStatementRepository: IPointsStatementRepository
    {
        private readonly IMembershipDbSqlConnectionFactory _membershipDbConnectionFactory;

        public PointsStatementRepository(IMembershipDbSqlConnectionFactory connectionFactory)
        {
            _membershipDbConnectionFactory = connectionFactory;
        }

        public async Task<List<PointsStatementTransaction>> GetPointsStatementTransactions(int memberId, int stateLive, int stateDeclined, int summaryId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "sp_PointsStatementTransaction";

            var parameters = new
            {
                MemberId = memberId,
                SummaryId = summaryId,
                StateLive = stateLive,
                StateDeclined = stateDeclined
            };

            var transactions = await sqlConnection.QueryAsync<PointsStatementTransaction>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return transactions.ToList();
        }

        public async Task<bool> DoesMemberHaveClubCreditTransactions(int memberId)
        {   
            var clubCredit = (int)TransactionType.ClubCredit;
            var clubCreditRefund = (int)TransactionType.ClubCreditRefund;
            var live = (int)TransactionState.Live;
            var na = (int)TransactionStatus.Na;

            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Rewards4Platform_GetMemberClubCreditTransactions";

            var parameters = new
            {
                MemberId = memberId,
                StateId =  live,
                StatusId = na,
                ClubCredit = clubCredit,
                ClubCreditRefund = clubCreditRefund
            };

            var memberHasClubCreditTransactions = await sqlConnection.QueryAsync<int>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return memberHasClubCreditTransactions.FirstOrDefault() > 0;
        }

        public async Task<List<PointsConversionRates>> GetPointsConversionRates()
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Rewards4Platform_GetPointsConversionRates";

            var result = await sqlConnection.QueryAsync<PointsConversionRates>(
                storedProc,
                commandType: CommandType.StoredProcedure
            );

            return result.ToList();
        }

        public async Task<List<CompetitionExpiryFreeMomentEntry>> GetFreeEntries(int memberId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            var result = await sqlConnection.QueryAsync<CompetitionExpiryFreeMomentEntry, CompetitionExpiryLog, CompetitionExpiryFreeMomentEntry>(
                "usp_Rewards4Platform_GetFreeEntries",
                (entry, log) =>
                {
                    entry.Log = log;
                    return entry;
                },
                new { MemberId = memberId },
                commandType: CommandType.StoredProcedure
            );

            return result.ToList();
        }
    }
}
