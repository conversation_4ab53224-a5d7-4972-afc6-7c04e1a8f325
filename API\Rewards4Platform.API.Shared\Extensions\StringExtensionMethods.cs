﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.Shared.Extensions
{
    public static class StringExtensionMethods
    {
        public static string ToJson(this object text)
        {
            if (text is null)
            {
                return string.Empty;
            }

            return JsonConvert.SerializeObject(text, new JsonSerializerSettings()
            {
                Formatting = Formatting.Indented,
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            });
        }
    }
}
