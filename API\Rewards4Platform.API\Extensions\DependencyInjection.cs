﻿using Polly;
using Rewards4Platform.API.Data.Clubs;
using Rewards4Platform.API.Data.Clubs.Repositories;
using Rewards4Platform.API.Data.MemberProfile;
using Rewards4Platform.API.Data.Membership;
using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Exceptions;
using Rewards4Platform.API.External.Emarsys.Repositories;
using Rewards4Platform.API.External.Emarsys.Services;
using Rewards4Platform.API.Services;
using Rewards4Platform.API.Services.BackgroundProcess;
using Rewards4Platform.API.Services.HttpClientServices;

namespace Rewards4Platform.API.Extensions
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddDependencyInjection(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
        {
            services.AddScoped<ITenantService, TenantService>();

            services.AddSingleton<IMembershipDbSqlConnectionFactory, MembershipDbSqlConnectionFactory>();
            services.AddSingleton<IClubsDbSqlConnectionFactory, ClubsDbSqlConnectionFactory>();
            services.AddSingleton<IMemberProfileDbSqlConnectionFactory, MemberProfileDbSqlConnectionFactory>();

            services.AddScoped<IJwtProvider, JwtProvider>();

            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IHashingService, HashingService>();
            services.AddScoped<IMemberService, MemberService>();
            services.AddScoped<ICommonService, CommonService>();
            services.AddScoped<IMerchantService, MerchantService>();
            services.AddScoped<IPointsConversionService, PointsConversionService>();
            services.AddScoped<IPointsStatementService, PointsStatementService>();
            services.AddScoped<IMemberService, MemberService>();
            services.AddScoped<ILoggingService, LoggingService>();
            services.AddScoped<IBackgroundProcessService, BackgroundProcessService>();
            services.AddScoped<IBackgroundProcessApiHttpClientService, BackgroundProcessApiHttpClientService>();
            services.AddScoped<IClubService, ClubService>();
            services.AddScoped<IApplicationService, ApplicationService>();
            services.AddScoped<IEmailService, EmarsysEmailService>();
            services.AddScoped<IEmarsysHttpClientService, EmarsysHttpClientService>();

            services.AddScoped<IAuthenticationRepository, AuthenticationRepository>();
            services.AddScoped<IMemberRepository, MemberRepository>();
            services.AddScoped<IMerchantRepository, MerchantRepository>();
            services.AddScoped<IClubsRepostiory, ClubsRepostiory>();
            services.AddScoped<IApplicationRepository, ApplicationRepository>();
            services.AddScoped<IPointsStatementRepository, PointsStatementRepository>();
            services.AddScoped<IEmarsysRepository, EmarsysRepository>();

            services.AddSingleton<IExceptionLogger, ExceptionLogger>();

            return services;
        }

        public static IServiceCollection AddHttpClientInjection(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
        {
            var regularTimeoutPolicy = Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(15));
            var longTimeoutPolicy = Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(30));

            # region Background Process API Http Client Injection
            services.AddHttpClient<IBackgroundProcessApiHttpClientService, BackgroundProcessApiHttpClientService>(config =>
            {
                config.BaseAddress = new Uri(configuration["BackgroundProcessApi:Url"]!);

                config.DefaultRequestHeaders.Add("X-Api-Key", configuration["BackgroundProcessApi:ApiKey"]);
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                return new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(5)
                };
            })
            .SetHandlerLifetime(Timeout.InfiniteTimeSpan)
            .AddTransientHttpErrorPolicy(policy => policy.WaitAndRetryAsync(3, _ => TimeSpan.FromSeconds(2)))
            .AddTransientHttpErrorPolicy(policy => policy.CircuitBreakerAsync(5, TimeSpan.FromSeconds(5)))
            .AddPolicyHandler(request => request.Method == HttpMethod.Get ? regularTimeoutPolicy : longTimeoutPolicy);
            #endregion

            #region Emarsys Http Client Injection
            services.AddHttpClient<IEmarsysHttpClientService, EmarsysHttpClientService>(config =>
            {
                config.BaseAddress = new Uri("https://api.emarsys.net/api/v2/");
            })
           .ConfigurePrimaryHttpMessageHandler(() =>
           {
               return new SocketsHttpHandler
               {
                   PooledConnectionLifetime = TimeSpan.FromMinutes(5)
               };
           })
           .SetHandlerLifetime(Timeout.InfiniteTimeSpan)
           .AddTransientHttpErrorPolicy(policy => policy.WaitAndRetryAsync(3, _ => TimeSpan.FromSeconds(2)))
           .AddTransientHttpErrorPolicy(policy => policy.CircuitBreakerAsync(5, TimeSpan.FromSeconds(5)))
           .AddPolicyHandler(request => request.Method == HttpMethod.Get ? regularTimeoutPolicy : longTimeoutPolicy);
            #endregion

            return services;
        }
    }
}
