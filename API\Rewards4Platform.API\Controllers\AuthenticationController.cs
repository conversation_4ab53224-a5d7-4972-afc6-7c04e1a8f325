﻿using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rewards4Platform.API.Extensions;
using Rewards4Platform.API.Services;
using Rewards4Platform.API.Shared.Constants;
using Rewards4Platform.API.Shared.Requests;
using Rewards4Platform.API.Validators;

namespace Rewards4Platform.API.Controllers
{
    [Route("api/authentication")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly ICommonService _commonService;

        public AuthenticationController(IAuthenticationService authenticationService, ICommonService commonService)
        {
            _authenticationService = authenticationService;
            _commonService = commonService;
        }

        [HttpPost]
        [Route("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegistrationRequest request, IValidator<RegistrationRequest> validator)
        {
            validator.ValidateAndThrow(request);

            var result = await _authenticationService.Register(request, Request);

            if (result.IsSuccess)
            {
                var cookieOptions = _commonService.SetCookieOptions();

                Response.Cookies.Append(Settings.AuthenticationTokenCookieName, result.Token, cookieOptions);

                result.Token = Request.IsApiTesting() ? result.Token : string.Empty;

                return CreatedAtAction(null, result);
            }

            return Ok(result);
        }

        [HttpPost]
        [Route("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequest request, IValidator<LoginRequest> validator)
        {
            validator.ValidateAndThrow(request);

            var result = await _authenticationService.Login(request, Request);

            if (result.IsSuccess)
            {
                var cookieOptions = _commonService.SetCookieOptions();

                Response.Cookies.Append(Settings.AuthenticationTokenCookieName, result.Token, cookieOptions);

                result.Token = Request.IsApiTesting() ? result.Token : string.Empty;
            }

            return Ok(result);
        }

        [HttpPost]
        [Route("logout")]
        public IActionResult Logout()
        {
            var cookieOptions = _commonService.SetCookieOptions();

            Response.Cookies.Delete(Settings.AuthenticationTokenCookieName, cookieOptions);

            return Ok();
        }

        [HttpPost]
        [Route("/reset-password/send-reset-password-link")]
        [AllowAnonymous]
        public async Task<IActionResult> SendResetPasswordEmail([FromBody] SendResetPasswordEmailRequest sendResetPasswordEmailRequest, IValidator<SendResetPasswordEmailRequest> validator)
        {
            validator.ValidateAndThrow(sendResetPasswordEmailRequest);
            await _authenticationService.SendResetPasswordEmail(sendResetPasswordEmailRequest, Request);
            return Ok();
        }

        [HttpPost]
        [Route("/reset-password/validate")]
        [AllowAnonymous]
        public async Task<IActionResult> ValidateResetPasswordCode([FromBody] ValidateResetPasswordCodeRequest validateResetPasswordCodeRequest, IValidator<ValidateResetPasswordCodeRequest> validator)
        {
            validator.ValidateAndThrow(validateResetPasswordCodeRequest);

            var validationResponse = await _authenticationService.ValidateResetPasswordCode(validateResetPasswordCodeRequest);
            return Ok(validationResponse);
        }

        [HttpPost]
        [Route("/reset-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest resetPasswordRequest, IValidator<ResetPasswordRequest> validator)
        {
            validator.ValidateAndThrow(resetPasswordRequest);

            var validationResponse = await _authenticationService.ValidateResetPasswordCode(validateResetPasswordCodeRequest);
            return Ok(validationResponse);
        }
    }
}
