﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rewards4Platform.API.Services;

namespace Rewards4Platform.API.Controllers
{
    [Route("api/merchant")]
    [ApiController]
    public class MerchantController : ControllerBase
    {
        private readonly IMerchantService _merchantService;
        public MerchantController(IMerchantService merchantService)
        {
            _merchantService = merchantService;
        }

        [HttpGet]
        [Route("details")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMerchantDetails(string urlName)
        {
            var response = await _merchantService.GetMerchantDetails(urlName);
            
            return Ok(response);
        }
    }
}
