﻿using FluentValidation;
using Rewards4Platform.API.Shared.Requests;

namespace Rewards4Platform.API.Validators
{
    public class ValidateResetPasswordCodeRequestValidator : AbstractValidator<ValidateResetPasswordCodeRequest>
    {
        public ValidateResetPasswordCodeRequestValidator()
        {
            RuleFor(x => x.MemberId)
                .GreaterThan(0)
                .WithMessage("Invalid MemberId");

            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("UserId is required");

            RuleFor(x => x.Code)
                .NotEmpty()
                .WithMessage("Reset password code is required");
        }
    }
}
