﻿using Rewards4Platform.API.Services.BackgroundProcess;
using Rewards4Platform.API.Shared.Models;

namespace Rewards4Platform.API.Services
{
    public interface ILoggingService
    {
        Task CreateLog(int typeId, int memberId, string note, int merchantId, Guid? customMerchantId, RemoteClientInformation client);
    }

    public class LoggingService : ILoggingService
    {
        private readonly IBackgroundProcessService _backgroundProcessService;

        public LoggingService(IBackgroundProcessService backgroundProcessService)
        {
            _backgroundProcessService = backgroundProcessService;
        }

        public async Task CreateLog(int typeId, int memberId, string note, int merchantId, Guid? customMerchantId, RemoteClientInformation client)
        {
            await _backgroundProcessService.CreateLog(typeId, memberId, note, merchantId, customMerchantId, client).ConfigureAwait(false);            
        }
    }
}
