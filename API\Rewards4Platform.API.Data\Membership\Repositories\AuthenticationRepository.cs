using Dapper;
using Microsoft.Identity.Client;
using Rewards4Platform.API.Data.Clubs.Repositories;
using Rewards4Platform.API.Shared.Models;
using System.Data;

namespace Rewards4Platform.API.Data.Membership.Repositories
{
    public interface IAuthenticationRepository
    {
        Task<int> CreateAccount(NewAccountDetails newAccountDetails);
        Task<ValidateMemberLoginDbSet> ValidateLogin(string applicationName, string hashedEmail);
        Task<PasswordDbSet?> GetMemberAccountPassword(Guid applicationId, Guid userId);
        Task UpdateMemberInfoAfterLoginAttempt(Guid applicationId, Guid userId, bool isPasswordCorrect);
        Task<CreateOrValidateResetPasswordKeyDbSet?> CreateOrValidateResetPasswordKey(bool isCreate, int memberId, Guid userId, string validationCode);
        Task<bool> ValidateResetPasswordCode(int? memberId, Guid? userId, string code);
    }

    public class AuthenticationRepository : IAuthenticationRepository
    {
        private readonly IMembershipDbSqlConnectionFactory _membershipDbConnectionFactory;
        private readonly IClubsRepostiory _clubsRepostiory;

        public AuthenticationRepository(IMembershipDbSqlConnectionFactory connectionFactory, IClubsRepostiory clubsRepostiory)
        {
            _membershipDbConnectionFactory = connectionFactory;
            _clubsRepostiory = clubsRepostiory;
        }

        public async Task<int> CreateAccount(NewAccountDetails newAccountDetails)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            var storeProc = "usp_Website_InsertNewMember";
            var parameters = new
            {
                userName = newAccountDetails.UserName,
                loweredUserName = newAccountDetails.LoweredUserName,
                usernameLogin = newAccountDetails.UsernameLogin,
                usernameEmail = newAccountDetails.UsernameEmail,
                memberReferralMemberId = newAccountDetails.MemberReferralMemberId, // set the REfeffal to 0
                memberSourceId = newAccountDetails.MemberSourceId,
                memberForename = newAccountDetails.MemberForename,
                applicationId = newAccountDetails.ApplicationId,
                applicationName = newAccountDetails.ApplicationName,
                password = newAccountDetails.Password,
                passwordSalt = newAccountDetails.PasswordSalt,
                email = newAccountDetails.Email,
                loweredEmail = newAccountDetails.LoweredEmail
            };

            var newMemberId = await sqlConnection.ExecuteScalarAsync<int>(
                                    storeProc,
                                    parameters,
                                    commandType: CommandType.StoredProcedure
            );            

            return newMemberId;
        }

        public async Task<PasswordDbSet?> GetMemberAccountPassword(Guid applicationId, Guid userId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = "usp_Website_GetMemberPassword";

            var parameters = new
            {
                ApplicationId = applicationId,
                UserId = userId
            };

            var passwordData = await sqlConnection.QueryFirstOrDefaultAsync<PasswordDbSet?>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return passwordData;
        }        

        public async Task<ValidateMemberLoginDbSet> ValidateLogin(string applicationName, string hashedEmail)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = "usp_Website_ValidateMemberLogin";

            var parameters = new
            {
                ApplicationName = applicationName,
                HashedEmail = hashedEmail
            };

            var validationResult = await sqlConnection.QueryFirstAsync<ValidateMemberLoginDbSet>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );
            
            return validationResult;
        }

        public async Task UpdateMemberInfoAfterLoginAttempt(Guid applicationId, Guid userId, bool isPasswordCorrect)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = "usp_Website_UpdatePostLoginAttemptMemberInfo";

            var parameters = new
            {
                ApplicationId = applicationId,
                UserId = userId,
                IsPasswordCorrect = isPasswordCorrect
            };

            await sqlConnection.ExecuteAsync(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );
        }

        public async Task<CreateOrValidateResetPasswordKeyDbSet?> CreateOrValidateResetPasswordKey(bool isCreate, int memberId, Guid userId, string validationCode)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = "usp_Website_CreateOrValidateResetPasswordKey";

            var parameters = new
            {
                IsCreate = isCreate,
                MemberId = memberId,
                UserId = userId,
                ValidationCode = validationCode
            };

            var resetPasswordKeyResult = await sqlConnection.QueryFirstOrDefaultAsync<CreateOrValidateResetPasswordKeyDbSet?>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return resetPasswordKeyResult;
        }

        public async Task<bool> ValidateResetPasswordCode(int? memberId, Guid? userId, string code)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            var storeProc = "usp_Website_ValidateResetPasswordCode";
            var parameters = new
            {
                MemberId = memberId ?? DBNull.Value,
                UserId = userId ?? DBNull.Value,
                ValidationCode = code
            };

            var isValidCode = await sqlConnection.QueryFirstOrDefaultAsync<bool>(
                                    storeProc,
                                    parameters,
                                    commandType: CommandType.StoredProcedure
            );

            return isValidCode;
        }
    }
}
