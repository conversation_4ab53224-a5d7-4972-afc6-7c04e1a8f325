﻿namespace Rewards4Platform.API.Shared.Models
{
    public class MemberDbSet
    {
        public string Application { get; set; } = string.Empty;
        public string Forename { get; set; } = string.Empty;
        public string Surname { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int MemberId { get; set; }
        public int AffiliateId { get; set; }
        public int QuidcoId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public Guid EmailVerification { get; set; }
        public string PointsAvailableText { get; set; } = string.Empty;
        public int PointsAvailable { get; set; }
        public decimal PointsAvailableDecimal { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string Postcode { get; set; } = string.Empty;
        public int GenderId { get; set; }
        public string MobileNumber { get; set; } = string.Empty;
        public DateTime AccountCreatedDate { get; set; }
        public DateTime LastPasswordChangedDate { get; set; }
        public bool MemberHasClubCredit { get; set; }
        public int ClubCreditAvailablePoints { get; set; }
        public string ClubCreditAvailableText { get; set; } = string.Empty;
        public string TotalAvailableSpend { get; set; } = string.Empty;
        public string EmailAddress { get; set; } = string.Empty;
    }
}
