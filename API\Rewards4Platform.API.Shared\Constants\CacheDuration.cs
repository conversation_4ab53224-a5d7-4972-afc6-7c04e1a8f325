﻿using System.Diagnostics.CodeAnalysis;

namespace Rewards4Platform.API.Shared.Constants
{
    [ExcludeFromCodeCoverage]
    public class CacheDuration
    {
        public class Merchant
        {
            public static DateTime SuppressedMerchants => DateTime.Now.AddMinutes(15);
            public static DateTime MerchantDetails => DateTime.Now.AddMinutes(15);
        }
        public class Member
        {
            public static DateTime MemberDetails => DateTime.Now.AddMinutes(10);

        }
    }
}
