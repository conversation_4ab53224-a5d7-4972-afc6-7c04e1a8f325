﻿using Dapper;
using System.Data;

namespace Rewards4Platform.API.Data.Clubs.Repositories
{
    public interface IClubsRepostiory
    {
        Task InsertClubMember(int memberId, int clubId);
    }

    public class ClubsRepostiory : IClubsRepostiory
    {
        private readonly IClubsDbSqlConnectionFactory _clubsDbSqlConnectionFactory;

        public ClubsRepostiory(IClubsDbSqlConnectionFactory clubsDbSqlConnectionFactory)
        {
            _clubsDbSqlConnectionFactory = clubsDbSqlConnectionFactory;
        }

        public async Task InsertClubMember(int memberId, int clubId)
        {
            await using var sqlConnection = _clubsDbSqlConnectionFactory.CreateConnection();

            var storeProc = "usp_Website_InsertClubMember";
            var parameters = new
            {
                memberId,
                clubId
            };

            await sqlConnection.ExecuteAsync(
                                    storeProc,
                                    parameters,
                                    commandType: CommandType.StoredProcedure
            );
        }
    }
}
