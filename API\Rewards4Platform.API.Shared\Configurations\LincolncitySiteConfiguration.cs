﻿namespace Rewards4Platform.API.Shared.Configurations
{
    public class LincolncitySiteConfiguration : ISiteConfiguration
    {
        public string ApplicationUrl { get; } = "lincolncity";
        public string ApplicationDisplayName { get; } = "Rewards4Imps";
        public string ApplicationName { get; } = "Rewards4Football";
        public bool IsClubSite { get; } = true;
        public int ClubId { get; } = 1027;
        public int OldClubId { get; } = 0;
        public string TeamName { get; } = "LincolnCity";
        public bool IsUsingCash { get; } = false;
        public string CashDescription { get; } = string.Empty;
        public bool IsUsingClubCredit { get; } = false;
        public string EmarsysEventPrefix { get; } = "Lincoln";
    }
}
