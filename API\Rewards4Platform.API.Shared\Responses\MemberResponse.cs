﻿namespace Rewards4Platform.API.Shared.Responses
{
    public class MemberResponse
    {
        public string Application { get; set; } = string.Empty;
        public string Forename { get; set; } = string.Empty;
        public string Surname { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int MemberId { get; set; }
        public int AffiliateId { get; set; }
        public int QuidcoId { get; set; }
        public Guid UserId { get; set; }
        public Guid EmailVerification { get; set; }
        public string PointsAvailableText { get; set; } = string.Empty;
        public int PointsAvailable { get; set; }
        public bool IsDeeplinkedIn { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string Postcode { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public int GenderId { get; set; }
        public DateTime AccountCreatedDate { get; set; }
        public bool HasClubCreditOnAccount { get; set; }
        public int ClubCreditPoints { get; set; }
        public string ClubCreditPointsValue { get; set; } = string.Empty;
        public string TotalAvailableToSpendValue { get; set; } = string.Empty;
    }
}
