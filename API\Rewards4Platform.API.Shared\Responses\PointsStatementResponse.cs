﻿using Newtonsoft.Json;

namespace Rewards4Platform.API.Shared.Responses
{
    public class PointsStatementResponse:ApiResponse
    {
        [JsonProperty("points")]
        public string Points { get; set; } = string.Empty;

        [JsonProperty("clubCredit")]
        public string ClubCredit { get; set; } = string.Empty;

        [JsonProperty("pointsAmount")]
        public string PointsAmount { get; set; } = string.Empty;

        [JsonProperty("price")]
        public string Price { get; set; } = string.Empty;

        [JsonIgnore]
        public DateTime Date { get; set; }

        [JsonProperty("pointsActiveDate")]
        public string PointsActiveDate { get; set; } = string.Empty;

        [JsonProperty("pointsActiveDateShort")]
        public string PointsActiveDateShort { get; set; } = string.Empty;

        [JsonProperty("transactionDate")]
        public string TransactionDate { get; set; } = string.Empty;

        [JsonProperty("transactionDateShort")]
        public string TransactionDateShort { get; set; } = string.Empty;

        [JsonProperty("transactionPendingHours")]
        public string TransactionPendingHours { get; set; } = "false";

        [JsonProperty("showPendingDropdown")]
        public string ShowPendingDropdown { get; set; } = "false";

        [JsonProperty("description")]
        public string Description { get; set; } = string.Empty;

        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;

        [JsonProperty("textClass")]
        public string TextClass { get; set; } = string.Empty;

        [JsonProperty("badgeClass")]
        public string BadgeClass { get; set; } = string.Empty;

        [JsonProperty("isFreeEntryAvailable")]
        public bool IsFreeEntryAvailable { get; set; }

        [JsonProperty("isFreeEntryUsed")]
        public bool IsFreeEntryUsed { get; set; }

        [JsonProperty("moreTransactionsAvailable")]
        public bool TransactionsAvailable { get; set; }
    }
}
