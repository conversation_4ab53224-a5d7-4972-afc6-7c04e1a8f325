﻿namespace Rewards4Platform.API.Shared.Models
{
    public class CompetitionExpiryFreeMomentEntry
    {
        public int Id { get; set; }

        public int LogId { get; set; }

        public int MemberId { get; set; }

        public DateTime DateAssigned { get; set; }

        public DateTime? DateUsed { get; set; }

        public Guid? CompetitionUsedId { get; set; }

        public CompetitionExpiryLog Log { get; set; } = new CompetitionExpiryLog();
    }
}
