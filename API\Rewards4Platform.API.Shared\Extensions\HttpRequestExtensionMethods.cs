﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Rewards4Platform.API.Shared.Configurations;
using Rewards4Platform.API.Shared.Models;

namespace Rewards4Platform.API.Extensions
{
    public static class HttpRequestExtensionMethods
    {     
        public static RemoteClientInformation GetRemoteClientDetails(this HttpRequest request)
        {
            var ipAddress = request.HttpContext.Connection.RemoteIpAddress!.ToString();
            var userAgent = request.Headers["User-Agent"].ToString();

            return new RemoteClientInformation(ipAddress, userAgent);
        }

        public static string GetHostname(this HttpRequest request)
        {
            var hostname = request.Host.Value.ToLower();

            if (hostname.Contains(":"))
            {
                return request.Host.Value.ToLower().Split(":")[0];
            }

            return hostname;
        }

        public static ISiteConfiguration GetSiteConfiguration(this HttpRequest request)
        {
            var hostname = GetHostname(request);            

            if (hostname.Contains("imps"))
            {
                return new LincolncitySiteConfiguration();
            }            

            throw new ArgumentException($"Cannot find site configuration for {hostname}");
        }

        public static bool IsRacingIrelandSite(this HttpRequest request)
        {
            var hostname = GetHostname(request);

            return hostname.Contains(".ie") || hostname.Contains("irish") || hostname.Contains("-ie");
        }

        public static string GetSourceIdFromUrl(this HttpRequest request)
        {
            var url = request.GetDisplayUrl();

            var hostname = new Uri(url).Host.ToLower();

            if (hostname.Contains(".irish") || hostname.Contains("-irish"))
            {
                return "Website - Racing Ireland";
            }

            return "Website";
        }

        public static bool IsApiTesting(this HttpRequest request)
        {
            var configuration = request.HttpContext.RequestServices.GetService<IConfiguration>();

            if (configuration == null)
            {
                throw new InvalidOperationException("HttpRequestExtensionMethods not configured. Call Configure() at startup.");
            }

            var headerKey = configuration["ApiTesting:HeaderKey"];
            var expectedValue = configuration["ApiTesting:HeaderValue"];

            if (string.IsNullOrWhiteSpace(headerKey) || string.IsNullOrWhiteSpace(expectedValue))
            {
                return false;
            }

            if (!request.Headers.TryGetValue(headerKey, out var actualValue))
            {
                return false;
            }

            return string.Equals(actualValue, expectedValue, StringComparison.OrdinalIgnoreCase);
        }
    }
}
