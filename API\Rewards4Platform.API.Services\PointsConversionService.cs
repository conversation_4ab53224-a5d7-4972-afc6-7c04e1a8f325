﻿using Polly;
using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Shared.Constants;
using Rewards4Platform.API.Shared.Models;
using StackExchange.Redis;
using StackExchange.Redis.Extensions.Core.Abstractions;

namespace Rewards4Platform.API.Services
{
    public interface IPointsConversionService
    {
        Task<decimal> ConvertPointsToValueAsync(string applicationName, int points);
        Task<decimal> GetConversionRateAsync(string applicationName);
    }
    public class PointsConversionService : IPointsConversionService
    {
        private readonly IPointsStatementRepository _pointsStatementRepository;
        private readonly IRedisDatabase _redis;
        private readonly string _cacheKey = CacheKey.PointsStatement.PointsConversionRatesCacheKey();
        public PointsConversionService(IRedisDatabase redis, IPointsStatementRepository pointsStatementRepository)
        {
            _pointsStatementRepository = pointsStatementRepository;
            _redis = redis;
        }

        public async Task<decimal> ConvertPointsToValueAsync(string applicationName, int points)
        {
            var conversionRate = await GetConversionRateAsync(applicationName);

            var pointsValue = Math.Round(points / conversionRate, 2);

            return pointsValue;
        }

        public async Task<decimal> GetConversionRateAsync(string applicationName)
        {
            var rates = await _redis.GetWithRetryAsync<List<PointsConversionRates>>(_cacheKey);

            if (rates == null)
            {
                await PopulatePointsConversionRatesAsync();
                rates = await _redis.GetWithRetryAsync<List<PointsConversionRates>>(_cacheKey);
            }

            return rates.Single(m => m.ApplicationName == applicationName).ConversionRate;
        }

        public async Task PopulatePointsConversionRatesAsync()
        {
            var data = _pointsStatementRepository.GetPointsConversionRates();
            var key = _cacheKey;
            await _redis.AddAsync(key, data);
        }
    }
     
}
