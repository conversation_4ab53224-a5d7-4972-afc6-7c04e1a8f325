﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace Rewards4Platform.API.Data.MemberProfile
{
    public interface IMemberProfileDbSqlConnectionFactory : ISqlConnectionFactory
    {
        
    }
    public class MemberProfileDbSqlConnectionFactory : IMemberProfileDbSqlConnectionFactory
    {
        private readonly IConfiguration _configuration;

        public MemberProfileDbSqlConnectionFactory(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public SqlConnection CreateConnection()
        {
            var connectionString = _configuration.GetConnectionString("MemberProfile");

            return new SqlConnection(connectionString);
        }
    }
}
