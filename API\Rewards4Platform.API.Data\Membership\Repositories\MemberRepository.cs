﻿using Dapper;
using Microsoft.Identity.Client;
using Rewards4Platform.API.Shared.Models;
using Rewards4Platform.API.Shared.Requests;
using Rewards4Platform.API.Shared.Responses;
using System.Data;

namespace Rewards4Platform.API.Data.Membership.Repositories
{
    public interface IMemberRepository
    {
        Task<MemberDbSet?> GetMember(int memberId, bool siteUsesClubCredit, bool expectsSingleMember = true, bool usePointsBalanceLimits = true);
        Task<int> GetMemberIdByApplicationNameAndEmail(string applicationName, string hashedEmail);
        Task<UserNameDbSet?> GetUsernameByApplicationNameAndEmail(string applicationName, string hashedEmail);
        Task<bool> IsRacingIrelandMember(int memberId);
        Task<MemberDbSet?> GetMemberDetails(string applicationName, int memberId);
        Task<UpdateMemberDbSet?> UpdateMemberDetails(int memberId, UpdateMemberRequest updateMemberRequest);
    }

    public class MemberRepository : IMemberRepository
    {
        private readonly IMembershipDbSqlConnectionFactory _membershipDbConnectionFactory;

        public MemberRepository(IMembershipDbSqlConnectionFactory membershipDbConnectionFactory)
        {
            _membershipDbConnectionFactory = membershipDbConnectionFactory;
        }

        public async Task<MemberDbSet?> GetMember(int memberId, bool siteUsesClubCredit, bool expectsSingleMember = true, bool usePointsBalanceLimits = true)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = usePointsBalanceLimits ? "sp_Rewards4Sport_GetMemberDetails" : "sp_Rewards4Sport_GetMemberDetails_NoPointsBalanceLimits";

            var parameters = new
            {
                MemberId = memberId,
                siteUsesClubCredit = siteUsesClubCredit
            };

            var member = await sqlConnection.QueryFirstOrDefaultAsync<MemberDbSet?>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );
            return member;
        }

        public async Task<int> GetMemberIdByApplicationNameAndEmail(string applicationName, string hashedEmail)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_GetMemberIdByApplicationNameAndEmail";

            var parameters = new
            {
                applicationName,
                hashedEmail
            };

            var memberId = await sqlConnection.ExecuteScalarAsync<int>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return memberId;
        }

        public async Task<UserNameDbSet?> GetUsernameByApplicationNameAndEmail(string applicationName, string hashedEmail)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();
            var storedProc = "Getaspnet_Usernames_ByApplicationNameEmailSuppress";

            var parameters = new
            {
                ApplicationName = applicationName,
                Email = hashedEmail,
                Suppress = false
            };

            var result = await sqlConnection.QueryFirstOrDefaultAsync(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            if (result == null)
            {
                return null;
            }

            var usernameInfo = new UserNameDbSet
            {
                UsernameId = result.usernameID,
                ApplicationName = result.ApplicationName,
                MemberId = result.memberID,
                UserId = result.UserID,
                UsernameLogin = result.usernameLogin,
                UsernameEmail = result.usernameEmail,
                UsernameMobile = result.usernameMobile,
                UsernameDate = result.usernameDate,
                UsernameSuppress = result.usernameSuppress
            };

            return usernameInfo;
        }

        public async Task<bool> IsRacingIrelandMember(int memberId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_CheckIfRacingIrelandMember";

            var parameters = new
            {
                memberId
            };

            var isRacingIrelandMember = await sqlConnection.ExecuteScalarAsync<bool>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return isRacingIrelandMember;
        }

        public async Task<MemberDbSet?> GetMemberDetails(string applicationName, int memberId)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Rewards4Platform_GetMemberDtails";

            var parameters = new
            {
                MemberId = memberId,
                ApplicationName = applicationName
            };

            var member = await sqlConnection.QueryFirstOrDefaultAsync<MemberDbSet?>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return member;
        }

        public async Task<UpdateMemberDbSet?> UpdateMemberDetails(int memberId, UpdateMemberRequest updateMemberRequest)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_UpdateMemberDetails";

            var parameters = new
            {
                MemberId = memberId,
                Firstname = updateMemberRequest.FirstName,
                Surname = updateMemberRequest.Surname,
                DateOfBirth = updateMemberRequest.DateOfBirth,
                MobileNumber = updateMemberRequest.MobileNumber,
                AddressFirstLine = updateMemberRequest.AddressFirstLine,
                Postcode = updateMemberRequest.Postcode
            };

            var member = await sqlConnection.QueryFirstOrDefaultAsync<UpdateMemberDbSet?>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return member;
        }
    }
}
