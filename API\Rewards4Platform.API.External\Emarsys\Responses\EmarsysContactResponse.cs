﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.External.Emarsys.Responses
{
    public class EmarsysContactResponse
    {
        [JsonProperty("3")] 
        public string Email { get; set; } = string.Empty;

        [JsonProperty("31")] 
        public string OptInStaus { get; set; } = string.Empty;

        [JsonProperty("EmailVerificationCode")] 
        public string EmailVerificationCode { get; set; } = string.Empty;

        [JsonProperty("HashedEmail")] 
        public string HashedEmail { get; set; } = string.Empty;

        [JsonProperty("UserId")] 
        public string UserId { get; set; } = string.Empty;

        [JsonProperty("1")] 
        public string FirstName { get; set; } = string.Empty;
    }
}
