﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.External.Emarsys.Responses
{
    public class EmarsysDataResponse
    {
        [JsonProperty("errors")]
        public List<EmarsysErrorResponse> Errors { get; set; }

        [JsonProperty("result")]
        public object Result { get; set; }

        public bool HasResult => Result.ToString().ToLower().Trim() != "false";
        public List<EmarsysContactResponse> Contacts => JsonConvert.DeserializeObject<List<EmarsysContactResponse>>(Result.ToString());

        [JsonProperty("id")]
        public string Id { get; set; }
        [JsonProperty("name")]
        public string Name { get; set; }
    }
}
