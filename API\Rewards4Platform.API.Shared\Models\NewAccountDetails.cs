﻿namespace Rewards4Platform.API.Shared.Models
{
    public class NewAccountDetails
    {
        public string UserName { get; set; } = string.Empty;
        public string LoweredUserName { get; set; } = string.Empty;
        public Guid UsernameLogin { get; set; }
        public string UsernameEmail { get; set; } = string.Empty;
        public int? MemberReferralMemberId { get; set; }
        public string MemberSourceId { get; set; } = string.Empty;
        public string MemberForename { get; set; } = string.Empty;
        public Guid ApplicationId { get; set; }
        public string ApplicationName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string PasswordSalt { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string LoweredEmail { get; set; } = string.Empty;
        public int ClubId { get; set; }
    }
}
