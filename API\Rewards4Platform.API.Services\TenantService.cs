﻿using Microsoft.AspNetCore.Http;
using Rewards4Platform.API.Shared.Configurations;
using Rewards4Platform.API.Shared.Exceptions;
using Rewards4Platform.API.Shared.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.Services
{
    public interface ITenantService
    {
        TenantContext GetCurrentTenant();
    }

    public sealed class TenantService : ITenantService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const string TenantHeaderName = "X-Tenant-Name";
        private const string TenantContextItemKey = "TenantContext";

        public TenantService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public TenantContext GetCurrentTenant()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                throw new InvalidOperationException("HTTP context is not available.");
            }

            // Check if tenant context is already resolved and cached in request
            if (httpContext.Items.TryGetValue(TenantContextItemKey, out var cachedTenant) && cachedTenant is TenantContext tenant)
            {
                return tenant;
            }

            var tenantHeader = httpContext.Request.Headers[TenantHeaderName].FirstOrDefault();

            if (string.IsNullOrWhiteSpace(tenantHeader))
            {                
                throw new TenantHeaderMissingException();
            }

            var tenantName = tenantHeader.Trim().ToLowerInvariant();
            
            var tenantConfiguration = GetTenantConfiguration(tenantName);
            var tenantContext = new TenantContext
            {
                TenantName = tenantHeader,
                TenantConfiguration = tenantConfiguration
            };

            // Cache in request context
            httpContext.Items[TenantContextItemKey] = tenantContext;

            return tenantContext;            
        }

        private ISiteConfiguration GetTenantConfiguration(string tenantName)
        {            
            return tenantName switch
            {
                "rewards4imps" => new LincolncitySiteConfiguration(),
                "rewards4racing" => new RacingSiteConfiguration(),
                _ => throw new TenantNotFoundException(tenantName)
            };
        }
    }
}
