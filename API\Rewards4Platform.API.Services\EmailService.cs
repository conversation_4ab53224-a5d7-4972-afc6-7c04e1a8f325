﻿using Rewards4Platform.API.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.Services
{
    public interface IEmailService
    {
        Task<bool> Send(string applicationName, int memberId, string emarsysPrefix, EmailEvent eventName, Dictionary<string, object> placeholderReplacements);
    }

    public class EmailService
    {
    }
}
