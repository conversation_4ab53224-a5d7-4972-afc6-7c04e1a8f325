﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace Rewards4Platform.API.Data.Clubs
{
    public interface IClubsDbSqlConnectionFactory : ISqlConnectionFactory
    {
    }

    public class ClubsDbSqlConnectionFactory : IClubsDbSqlConnectionFactory
    {
        private readonly IConfiguration _configuration;

        public ClubsDbSqlConnectionFactory(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public SqlConnection CreateConnection()
        {
            var connectionString = _configuration.GetConnectionString("Clubs");

            return new SqlConnection(connectionString);
        }
    }
}
