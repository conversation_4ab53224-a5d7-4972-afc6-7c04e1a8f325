﻿namespace Rewards4Platform.API.Shared.Models
{
    public class ValidateMemberLoginDbSet
    {
        public bool AccountExists { get; set; }
        public bool AccountLocked { get; set; }
        public Guid? ApplicationId { get; set; }
        public Guid? UserId { get; set; }
        public int MemberId { get; set; }
        public string? Forename { get; set; } = string.Empty;
        public string? UsernameEmail { get; set; } = string.Empty;
        public Guid? UsernameLogin { get; set; }
    }
}
