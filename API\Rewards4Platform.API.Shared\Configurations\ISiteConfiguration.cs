﻿namespace Rewards4Platform.API.Shared.Configurations
{
    public interface ISiteConfiguration
    {
        string ApplicationUrl { get; }
        string ApplicationDisplayName { get; }
        string ApplicationName { get; }
        bool IsClubSite { get; }
        int OldClubId { get; }
        int ClubId { get; }
        string TeamName { get; }
        public bool IsUsingCash { get; }
        public string CashDescription { get; }
        bool IsUsingClubCredit { get; }
        string EmarsysEventPrefix { get; }
    }
}
