﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Rewards4Platform.API.Shared.Constants;
using Rewards4Platform.API.Shared.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Rewards4Platform.API.Services
{
    public interface IJwtProvider
    {
        Task<string> GenerateToken(MemberClaim memberClaims);
        MemberInfoFromJwtToken? GetMemberInfoFromToken(HttpRequest httpRequest);
    }

    public class JwtProvider : IJwtProvider
    {
        private readonly IConfiguration _configuration;
        private const string DeepLinkClaimType = "DeepLink";

        public JwtProvider(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<string> GenerateToken(MemberClaim memberClaims)
        {
            var claims = new Claim[] 
            {
                new(JwtRegisteredClaimNames.Sub, memberClaims.MemberId.ToString()),
                new(JwtRegisteredClaimNames.Email, memberClaims.Email),
                new(JwtRegisteredClaimNames.Name, memberClaims.Name),
                new(DeepLinkClaimType, memberClaims.IsDeepLinkLogin.ToString()),
            };

            var signingCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:SecretKey"]!)),
                    SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                    _configuration["Jwt:Issuer"],
                    _configuration["Jwt:Audience"],
                    claims,
                    null,
                    DateTime.Now.AddHours(6),
                    signingCredentials);

            var tokenValue = new JwtSecurityTokenHandler().WriteToken(token);

            return await Task.FromResult(tokenValue);
        }

        public MemberInfoFromJwtToken? GetMemberInfoFromToken(HttpRequest httpRequest)
        {
            if(!httpRequest.Cookies.TryGetValue(Settings.AuthenticationTokenCookieName, out var token) || string.IsNullOrEmpty(token))
            {
                return null;
            }

            if (!string.IsNullOrEmpty(token))
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(token);

                var memberIdClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub)?.Value;
                var emailClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Email)?.Value;
                var nameClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Name)?.Value;
                var isDeepLinkLogin = jwtToken.Claims.FirstOrDefault(c => c.Type == DeepLinkClaimType)?.Value;

                if (!string.IsNullOrWhiteSpace(memberIdClaim) && !string.IsNullOrWhiteSpace(emailClaim) && !string.IsNullOrWhiteSpace(nameClaim) && !string.IsNullOrWhiteSpace(isDeepLinkLogin))
                {
                    return new MemberInfoFromJwtToken
                    {
                        MemberId = int.Parse(memberIdClaim),
                        Email = emailClaim,
                        Name = nameClaim,
                        IsDeepLinkLogin = isDeepLinkLogin.ToLower() is "true" ? true : false
                    };
                }
            }

            return null;
        }
    }
}
