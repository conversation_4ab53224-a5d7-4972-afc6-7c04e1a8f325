﻿using FluentValidation;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

namespace Rewards4Platform.API.Exceptions
{
    public class GlobalExceptionHandler : IExceptionHandler
    {
        private readonly IExceptionLogger _exceptionLogger;

        public GlobalExceptionHandler(IExceptionLogger exceptionLogger)
        {
            _exceptionLogger = exceptionLogger;
        }

        public async ValueTask<bool> TryHandleAsync(
         HttpContext httpContext,
         Exception exception,
         CancellationToken cancellationToken)
        {
            ProblemDetails problemDetails;

            // Handle Validation errors
            if (exception is ValidationException vex)
            {
                problemDetails = new ProblemDetails
                {
                    Status = StatusCodes.Status400BadRequest,
                    Title = "One or more validation errors occurred.",
                    Detail = "See the errors property for more information.",
                    Instance = httpContext.Request.Path
                };

                // attach validation errors
                problemDetails.Extensions["errors"] = vex.Errors.Select(e => new
                {
                    field = e.PropertyName,
                    message = e.ErrorMessage
                });

                httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
            }
            else
            {
                var statusCode = exception switch
                {
                    UnauthorizedAccessException => StatusCodes.Status401Unauthorized,
                    InvalidOperationException => StatusCodes.Status403Forbidden,
                    ArgumentException => StatusCodes.Status400BadRequest,
                    _ => StatusCodes.Status500InternalServerError
                };

                problemDetails = new ProblemDetails
                {
                    Status = statusCode,
                    Title = "An error occurred while processing your request.",
                    Detail = exception.Message,
                    Instance = httpContext.Request.Path
                };

                httpContext.Response.StatusCode = statusCode;
            }

            httpContext.Response.ContentType = "application/problem+json";
            await httpContext.Response.WriteAsJsonAsync(problemDetails, cancellationToken);

            await _exceptionLogger.LogException(exception);
            return true;
        }
    }
}
