﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.18" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.8" />
    <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Rewards4Platform.Common\Rewards4Platform.Common.csproj" />
    <ProjectReference Include="..\Rewards4Platform.API.External\Rewards4Platform.API.External.csproj" />
    <ProjectReference Include="..\Rewards4Platform.API.Services\Rewards4Platform.API.Services.csproj" />
    <ProjectReference Include="..\Rewards4Platform.API.Shared\Rewards4Platform.API.Shared.csproj" />
  </ItemGroup>

</Project>