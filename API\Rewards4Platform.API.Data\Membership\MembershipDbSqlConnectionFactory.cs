﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace Rewards4Platform.API.Data.Membership
{
    public interface IMembershipDbSqlConnectionFactory : ISqlConnectionFactory
    {
        
    }

    public class MembershipDbSqlConnectionFactory : IMembershipDbSqlConnectionFactory
    {
        private readonly IConfiguration _configuration;

        public MembershipDbSqlConnectionFactory(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public SqlConnection CreateConnection()
        {
            var connectionString = _configuration.GetConnectionString("Membership");

            return new SqlConnection(connectionString);
        }
    }
}
