﻿using Dapper;
using System.Data;

namespace Rewards4Platform.API.Data.Membership.Repositories
{
    public interface IApplicationRepository
    {
        Task<Guid> GetApplicationId(string applicationName);
    }

    public class ApplicationRepository : IApplicationRepository
    {
        private readonly IMembershipDbSqlConnectionFactory _membershipDbConnectionFactory;
        public ApplicationRepository(IMembershipDbSqlConnectionFactory membershipDbConnectionFactory)
        {
            _membershipDbConnectionFactory = membershipDbConnectionFactory;
        }

        public async Task<Guid> GetApplicationId(string applicationName)
        {
            await using var sqlConnection = _membershipDbConnectionFactory.CreateConnection();

            const string storedProc = "usp_Website_GetApplicationId";

            var parameters = new
            {
                applicationName
            };

            var applicationId = await sqlConnection.ExecuteScalarAsync<Guid>(
                storedProc,
                parameters,
                commandType: CommandType.StoredProcedure
            );

            return applicationId;
        }
    }
}
