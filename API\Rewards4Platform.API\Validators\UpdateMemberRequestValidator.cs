﻿using FluentValidation;
using Polly;
using Rewards4Platform.API.Services;
using Rewards4Platform.API.Shared.Constants;
using Rewards4Platform.API.Shared.Requests;
using System.Text.RegularExpressions;

namespace Rewards4Platform.API.Validators
{
    public class UpdateMemberRequestValidator : AbstractValidator<UpdateMemberRequest>
    {        
        private readonly IHttpContextAccessor _httpContextAccessor;
        public UpdateMemberRequestValidator(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
            var context = _httpContextAccessor.HttpContext;
            var tenantService = context!.RequestServices.GetRequiredService<ITenantService>();

            var tenant = tenantService.GetCurrentTenant();
            var isRacingIrelandSite = tenant.TenantName is "Rewards4RacingIreland";

            RuleFor(x => x.FirstName)
                .NotEmpty().WithMessage("First name is required.")
                .MinimumLength(2).WithMessage("First name must be at least 2 characters long.")
                .MaximumLength(50).WithMessage("First name cannot exceed 50 characters.")
                .Must(name => ValidatorMethods.IsValidName(name)).WithMessage("First name must contain alphabetic characters only.");

            RuleFor(x => x.Surname)
               .NotEmpty().WithMessage("Surname is required.")
               .MinimumLength(2).WithMessage("Surname must be at least 2 characters long.")
               .MaximumLength(50).WithMessage("Surname cannot exceed 50 characters.")
               .Must(name => ValidatorMethods.IsValidName(name)).WithMessage("Surname must contain alphabetic characters only.");

            RuleFor(x => x.DateOfBirth)
                .NotEmpty().WithMessage("Date of birth is required.")
                .LessThan(DateTime.Today).WithMessage("Date of birth must be in the past.")
                .Must(date => date <= DateTime.Today.AddYears(-Settings.MinimumAgeOfMember))
                .WithMessage($"Member must be at least {Settings.MinimumAgeOfMember} years old.");

            RuleFor(x => x.MobileNumber)
                .Must(mobile => ValidatorMethods.IsValidMobileNumber(mobile, isRacingIrelandSite))
                .WithMessage("Your mobile number is invalid.")
                .When(x => !string.IsNullOrEmpty(x.MobileNumber));

            RuleFor(x => x.AddressFirstLine)
                .NotEmpty().WithMessage("Address is required.")
                .MinimumLength(2).WithMessage("Address must be at least 2 characters long.")
                .When(x => !string.IsNullOrEmpty(x.AddressFirstLine));

            RuleFor(x => x.Postcode)
                .Must(postcode => ValidatorMethods.IsValidPostcode(postcode))
                .WithMessage("Postcode is invalid.")
                .When(x => !string.IsNullOrEmpty(x.Postcode) && !isRacingIrelandSite);
        }
    }
}
