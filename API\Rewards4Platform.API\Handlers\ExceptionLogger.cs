﻿using Exceptionless;

namespace Rewards4Platform.API.Exceptions
{
    public interface IExceptionLogger
    {
        Task LogException(Exception exception);
    }

    public class ExceptionLogger : IExceptionLogger
    {
        public async Task LogException(Exception exception)
        {
            ExceptionlessClient.Default.SubmitException(exception);

            await Task.CompletedTask;
        }
    }
}
