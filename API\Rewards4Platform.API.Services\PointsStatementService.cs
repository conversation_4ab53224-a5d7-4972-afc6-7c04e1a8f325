﻿using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Extensions;
using Rewards4Platform.API.Shared.Enums;
using Rewards4Platform.API.Shared.Models;
using Rewards4Platform.API.Shared.Responses;
using Rewards4Platform.Common.Extensions;
using System.Globalization;

namespace Rewards4Platform.API.Services
{
    public interface IPointsStatementService
    {
        Task<IList<PointsStatementResponse>> GetPointsStatementAsync(string applicationDisplayName,
            string applicationName, int memberId,
            bool siteUsesClubCredit, bool isStatementPage, bool isUsingCash = false,
            int? page = 0, int? pageSize = 25);
    }
    
    public class PointsStatementService : IPointsStatementService
    {
        private readonly IPointsStatementRepository _pointsStatementRepository;
        private readonly IPointsConversionService _pointsConversionService;
        public PointsStatementService(IPointsStatementRepository pointsStatementRepository,IPointsConversionService pointsConversionService)
        {
            _pointsStatementRepository = pointsStatementRepository;
            _pointsConversionService = pointsConversionService;
        }

        public async Task<IList<PointsStatementResponse>> GetPointsStatementAsync(string applicationDisplayName,
            string applicationName, int memberId,
            bool siteUsesClubCredit, bool isStatementPage, bool isUsingCash = false,
            int? page = 0, int? pageSize = 25)
        {
            var stateLive = 1;
            var stateDeclined = 4;
            var pendingStatusId = 2;
            var summaryID = 22; // point deletion transaction

            try
            {
                var transactions = await _pointsStatementRepository.GetPointsStatementTransactions(memberId, stateLive, stateDeclined, 0);
                if (transactions == null)
                {
                    return new List<PointsStatementResponse>
                    {
                        new PointsStatementResponse
                        {
                            IsSuccess = false,
                            Message = "No transaction found."
                        }
                    };

                }

                #region Excluding Pending Betting Transactions from Member Statements 
                var excludedTransactionSources = new List<string> { "bet365import", "betfredimport", "betfairimport", "paddypowerimport" }; ;

                transactions = transactions.Where(x =>
                                                    !(
                                                        excludedTransactionSources.Contains(x.TransactionSourceId.ToLower()) &&
                                                        x.StateId == stateLive &&
                                                        x.StatusId == pendingStatusId &&
                                                        x.TransactionSuppress == false)
                                                  ).ToList();

                #endregion

                var transactionsStillAvailable = transactions.Count() - ((page.Value + 1) * pageSize.Value);

                transactions = transactions.Skip(page.Value * pageSize.Value).Take(pageSize.Value).ToList();

                var result = new List<PointsStatementResponse>();

                var culture = new CultureInfo("en-GB");

                var memberHasClubCreditTransactions = false;

                memberHasClubCreditTransactions = await _pointsStatementRepository.DoesMemberHaveClubCreditTransactions(memberId).ConfigureAwait(false);

                if (memberHasClubCreditTransactions)
                {
                    await _pointsConversionService.ConvertPointsToValueAsync(applicationName, 0);
                }

                foreach (var t in transactions)
                {
                    var status = GetTransactionStatus(t.StateId, t.TypeId, t.StatusId, t.TransactionActiveDate);
                    var textClass = GetTextClass(status, t.TransactionPoints, t.TypeId);
                    var badgeClass = textClass.Replace("text", "badge");
                    var transactionActiveDate = GetTransactionActiveDate(t);
                    var transactionActiveDateShort = GetTransactionActiveDate(t, true);

                    var transactionPendingHours = "false";
                    if (status == "pending" && t.TransactionActiveDate <= DateTime.Now)
                    {
                        transactionPendingHours = "true";
                    }

                    var showDropdown = "false";
                    if (t.StateId == (int)TransactionState.Live
                        && t.TypeId == (int)TransactionType.OnlineShopping
                        && t.StatusId == (int)TransactionStatus.Pending)
                    {
                        showDropdown = "true";
                    }

                    if (t.StateId == (int)TransactionState.Rejected)
                    {
                        showDropdown = "true";
                    }

                    var response = new PointsStatementResponse
                    {
                        TransactionDate = t.TransactionDate.Date.ToString("d", culture),
                        TransactionDateShort = t.TransactionDate.Date.ToOrdinalDate(showDay: false, showShortMonth: true, showShortYear: true),
                        TransactionPendingHours = transactionPendingHours,
                        PointsActiveDate = transactionActiveDate,
                        PointsActiveDateShort = transactionActiveDateShort,
                        BadgeClass = badgeClass,
                        Description = t.DescriptionText,
                        Status = status,
                        TextClass = textClass,
                        ShowPendingDropdown = showDropdown,
                        Date = t.TransactionDate,
                        TransactionsAvailable = transactionsStillAvailable > 0
                    };

                    var clubCashAmount = string.Empty;
                    var pointsLabel = await SetPointsLabel(t, siteUsesClubCredit, applicationName, isUsingCash);
                    var pointsAmountLabel = await SetPointsAmountLabel(t, siteUsesClubCredit, applicationName, isUsingCash);
                    var priceLabel = t.TransactionPrice.ToString("C", culture);

                    if (memberHasClubCreditTransactions)
                    {
                        clubCashAmount = await GetClubCashAmount(applicationName, t.ClubCashAmount);
                    }

                    InitStatementValuesToDisplay(t, response, clubCashAmount, priceLabel, pointsLabel, pointsAmountLabel);

                    if (siteUsesClubCredit)
                    {
                        result.Add(response);
                    }
                    else
                    {
                        if (t.TypeId != (int)TransactionType.ClubCredit && t.TypeId != (int)TransactionType.ClubCreditRefund)
                        {
                            result.Add(response);
                        }
                    }
                }
                await AddFreeEntryInformation(memberId, result, culture);

                result = result.OrderByDescending(o => o.Date).ToList();

                if (result.Count == 0)
                {
                    return EmptyPointsStatement(applicationDisplayName, culture, isStatementPage);
                }

                return result;
            }

            catch (Exception ex)
            {
                return new List<PointsStatementResponse>
                {
                    new PointsStatementResponse
                    {
                        IsSuccess = false,
                        Message = "An error occurred: " + ex.Message
                    }
                };
            }

        }
            
        
        private string GetTransactionStatus(int stateId, int typeId, int statusId, DateTime pointsActiveDate)
        {
            if (stateId == (int)TransactionState.Rejected)
            {
                return "declined";
            }

            if (stateId == (int)TransactionState.Live)
            {
                if (typeId == (int)TransactionType.ClubCreditRefund || typeId == (int)TransactionType.ClubCredit)
                {
                    return "paid";
                }

                if (typeId == (int)TransactionType.Redeem)
                {
                    return "spent";
                }

                if (typeId == (int)TransactionType.OnlineShopping && statusId == (int)TransactionStatus.Pending)
                {
                    return "pending";
                }

                if (typeId == (int)TransactionType.OnlineShopping)
                {
                    return "paid";
                }

                if (typeId == (int)TransactionType.RedeemRelatedRefund)
                {
                    return "refunded";
                }

                if (typeId == (int)TransactionType.R4G && pointsActiveDate > DateTime.Now)
                {
                    return "pending";
                }

                if (typeId == (int)TransactionType.R4G && pointsActiveDate <= DateTime.Now)
                {
                    return "paid";
                }

                if (typeId == (int)TransactionType.R4GRefund && pointsActiveDate > DateTime.Now)
                {
                    return "pending";
                }

                if (typeId == (int)TransactionType.R4GRefund && pointsActiveDate <= DateTime.Now)
                {
                    return "refund";
                }

                if (typeId == (int)TransactionType.CampaignPoints && statusId == (int)TransactionStatus.Pending)
                {
                    return "pending";
                }

                if (typeId == (int)TransactionType.CampaignPoints && statusId == (int)TransactionStatus.Paid)
                {
                    return "paid";
                }

                if (typeId == (int)TransactionType.PointsAllocationCampaign)
                {
                    return "paid";
                }

                if (typeId == (int)TransactionType.PointsExpiry)
                {
                    return "expired";
                }
            }

            return "paid";
        }

        private string GetTextClass(string status, int points, int typeId)
        {
            status = status.ToLower();

            if (status == "expired")
            {
                return "text-dark";
            }

            if (status == "spent")
            {
                return "text-dark";
            }

            if (status == "paid")
            {
                return "text-success";
            }

            if (status == "pending")
            {
                return "text-warning";
            }

            if (status == "declined")
            {
                return "text-danger";
            }

            if (status == "refund" || status == "refunded")
            {
                return "text-dark";
            }

            return string.Empty;
        }

        private string GetTransactionActiveDate(PointsStatementTransaction transaction, bool shortDate = false)
        {
            if (transaction.StateId == (int)TransactionState.Live &&
                transaction.TypeId == (int)TransactionType.Redeem)
            {
                return string.Empty;
            }

            if (transaction.StateId == (int)TransactionState.Live &&
                transaction.TypeId == (int)TransactionType.RedeemRelatedRefund)
            {
                return string.Empty;
            }

            if (transaction.StateId == (int)TransactionState.Live &&
                transaction.TypeId == (int)TransactionType.PointsExpiry)
            {
                return string.Empty;
            }

            if (shortDate)
            {
                return transaction.TransactionActiveDate.Date.ToOrdinalDate(showDay: false, showShortMonth: true,
                    showShortYear: true);
            }

            var culture = new CultureInfo("en-GB");

            return transaction.TransactionActiveDate.Date.ToString("d", culture);
        }

        private async Task<string> SetPointsLabel(PointsStatementTransaction transaction, bool siteUsesClubCredit, string applicationName = "",
            bool isUsingCash = false)
        {
            if (transaction.IsClubCreditTransaction(siteUsesClubCredit))
            {
                return string.Empty;
            }

            if (isUsingCash)
            {
                var value = await _pointsConversionService.ConvertPointsToValueAsync(applicationName, transaction.TransactionPoints);
                var formattedPointsValue = value.ToString("F2");
                return transaction.TransactionPoints > 0 ? $"+£{formattedPointsValue}" : $"-£{formattedPointsValue.ToString(CultureInfo.InvariantCulture).Replace("-", string.Empty)}";
            }

            return transaction.TransactionPoints > 0 ? $"+{transaction.TransactionPoints} PTS" : $"{transaction.TransactionPoints} PTS";
        }
        private async Task<string> SetPointsAmountLabel(PointsStatementTransaction transaction, bool siteUsesClubCredit, string applicationName = "",
           bool isUsingCash = false)
        {
            if (transaction.IsClubCreditTransaction(siteUsesClubCredit))
            {
                return string.Empty;
            }

            if (isUsingCash)
            {
                if (transaction.PointsAmount != null)
                {
                    var value = await _pointsConversionService.ConvertPointsToValueAsync(applicationName, (int)transaction.PointsAmount);
                    return transaction.TransactionPoints > 0 ? $"+£{value}" : $"-£{value.ToString(CultureInfo.InvariantCulture).Replace("-", string.Empty)}";
                }
            }
            return transaction.PointsAmount > 0 ? $"+{transaction.PointsAmount} PTS" : $"{transaction.PointsAmount} PTS";
        }

        private async Task<string> GetClubCashAmount(string applicationName, int? clubCashAmount)
        {
            if (clubCashAmount.HasValue == false)
            {
                return string.Empty;
            }

            var clubCreditAmountAsString =
                (await _pointsConversionService.ConvertPointsToValueAsync(applicationName, clubCashAmount.Value)).ToString("0.00");

            if (clubCreditAmountAsString.Contains("-"))
            {
                clubCreditAmountAsString = clubCreditAmountAsString.Replace("-", "");
                return $"-£{clubCreditAmountAsString}";
            }
            return $"£{clubCreditAmountAsString}";
        }
        private PointsStatementResponse InitStatementValuesToDisplay(PointsStatementTransaction transaction, PointsStatementResponse response,
            string clubCredit, string price, string points, string pointsAmount)
        {
            response.Price = string.Empty;
            response.ClubCredit = string.Empty;
            response.PointsAmount = string.Empty;
            response.Points = string.Empty;

            if (transaction.TypeId == (int)TransactionType.ClubCredit ||
                transaction.TypeId == (int)TransactionType.ClubCreditRefund)
            {
                response.Price = price;
            }

            if (transaction.TypeId == (int)TransactionType.Redeem ||
               transaction.TypeId == (int)TransactionType.RedeemRelatedRefund)
            {
                if (transaction.PointsAmount != null && transaction.PointsAmount != 0)
                {
                    response.PointsAmount = pointsAmount;
                }

                if (transaction.ClubCashAmount != null && transaction.ClubCashAmount != 0)
                {
                    response.ClubCredit = clubCredit;
                }

                if (transaction.ClubCashAmount == null && transaction.PointsAmount == null)
                {
                    response.Points = points;
                }

                return response;
            }

            response.Points = points;
            return response;
        }

        private async Task AddFreeEntryInformation(int memberId, List<PointsStatementResponse> results, CultureInfo culture)
        {
            var freeEntries = await _pointsStatementRepository.GetFreeEntries(memberId);

            foreach (var entry in freeEntries)
            {
                var isFreeEntyAvailable = entry.DateUsed == null;

                results.Add(new PointsStatementResponse()
                {
                    TransactionDate = entry.DateAssigned.Date.ToString("d", culture),
                    TransactionDateShort = entry.DateAssigned.Date.ToOrdinalDate(showDay: false, showShortMonth: true,
                        true),
                    TransactionPendingHours = string.Empty,
                    PointsActiveDate = entry.DateAssigned.Date.ToString("d", culture),
                    PointsActiveDateShort = entry.DateAssigned.Date.ToOrdinalDate(false, true, true),
                    BadgeClass = string.Empty,
                    Description = isFreeEntyAvailable
                        ? "Free entry into any competition"
                        : "FREE Entry Used - " + entry.Log.CompetitionTitle,
                    Points = string.Empty,
                    Status = isFreeEntyAvailable ? "Available to use" : "Spent",
                    TextClass = isFreeEntyAvailable ? "text-success" : "text-dark",
                    ShowPendingDropdown = "false",
                    IsFreeEntryAvailable = isFreeEntyAvailable,
                    IsFreeEntryUsed = !isFreeEntyAvailable,
                    Date = entry.DateAssigned
                });
            }
        }

        private static IList<PointsStatementResponse> EmptyPointsStatement(string applicationDisplayName,
            CultureInfo culture, bool isStatementPage)
        {
            if (isStatementPage)
            {
                return new List<PointsStatementResponse>()
                {
                    new PointsStatementResponse()
                    {
                        Description = "Hi {{name}}! Welcome to " + applicationDisplayName +
                                      "! As you collect and spend your points you will see all your updates here.",
                        TransactionDate = DateTime.Today.ToString("d", culture),
                        TransactionDateShort =
                            DateTime.Today.ToOrdinalDate(showDay: false, showShortMonth: true, showShortYear: true),
                        ClubCredit = string.Empty,
                        Status = "default"
                    }
                };
            }

            return new List<PointsStatementResponse>()
            {
                new PointsStatementResponse()
                {
                    Description = "Hi {{name}}! welcome to " + applicationDisplayName,
                    Points = "",
                    TransactionDate = DateTime.Today.ToString("d", culture),
                    TransactionDateShort =
                        DateTime.Today.ToOrdinalDate(showDay: false, showShortMonth: true, showShortYear: true),
                    Status = "default"
                },
                new PointsStatementResponse()
                {
                    Description = "Collecting points is really simple, visit the collect points page to find out more.",
                    Points = "",
                    TransactionDate = DateTime.Today.ToString("d", culture),
                    TransactionDateShort =
                        DateTime.Today.ToOrdinalDate(showDay: false, showShortMonth: true, showShortYear: true),
                    Status = "default"
                }
            };
        }
    }
}

