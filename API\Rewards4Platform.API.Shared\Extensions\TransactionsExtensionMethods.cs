﻿using Rewards4Platform.API.Shared.Enums;
using Rewards4Platform.API.Shared.Models;

namespace Rewards4Platform.API.Extensions
{
    public static class TransactionsExtensionMethods
    {
        public static bool IsClubCreditTransaction(this PointsStatementTransaction transaction, bool isClubCreditSite)
        {
            if (isClubCreditSite == false)
                return false;

            if (transaction.TypeId != (int)TransactionType.ClubCredit&&
                transaction.TypeId != (int)TransactionType.ClubCreditRefund)
                return false;

            if (transaction.StateId != (int)TransactionState.Live)
                return false;

            if (transaction.StatusId != (int)TransactionStatus.Na)
                return false;

            return true;
        }
    }
}
