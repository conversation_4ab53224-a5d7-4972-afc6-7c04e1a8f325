﻿using System.Text.RegularExpressions;

namespace Rewards4Platform.API.Validators
{
    public static class ValidatorMethods
    {
        public static bool IsValidPassword(string password)
        {
            var hasUpperCase = Regex.IsMatch(password, @"[A-Z]");
            var hasLowerCase = Regex.IsMatch(password, @"[a-z]");
            var hasDigit = Regex.IsMatch(password, @"\d");
            var hasSpecialChar = Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]");

            return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar;
        }

        public static bool IsValidMobileNumber(string mobile, bool isRacingIrelandSite)
        {
            if (string.IsNullOrEmpty(mobile))
            {
                return false;
            }

            if (isRacingIrelandSite)
            {
                var irelandRegex = new System.Text.RegularExpressions.Regex("^(?:\\+|00) ?([1-9]){3} ?\\d{9,10}$");
                return irelandRegex.IsMatch(mobile);
            }
            else
            {
                var ukRegex = new System.Text.RegularExpressions.Regex("^(((\\+44\\s?\\d{4}|\\(?0\\d{4}\\)?)\\s?\\d{3}\\s?\\d{3})|((\\+44\\s?\\d{3}|\\(?0\\d{3}\\)?)\\s?\\d{3}\\s?\\d{4})|((\\+44\\s?\\d{2}|\\(?0\\d{2}\\)?)\\s?\\d{4}\\s?\\d{4}))(\\s?\\#(\\d{4}|\\d{3}))?$");
                return ukRegex.IsMatch(mobile);
            }
        }

        public static bool IsValidPostcode(string postcode)
        {
            if (string.IsNullOrEmpty(postcode))
            {
                return false;
            }

            postcode = postcode.Trim().ToUpper();

            var postcodeRegex =
                new Regex(
                    "^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$");

            return postcodeRegex.IsMatch(postcode) != false;
        }

        public static bool IsValidName(string name)
        {
            var nameRegex = new Regex("^[A-Za-z]+$");
            return nameRegex.IsMatch(name);
        }
    }
}
