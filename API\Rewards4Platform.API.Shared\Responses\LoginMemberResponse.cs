﻿namespace Rewards4Platform.API.Shared.Responses
{
    public class LoginMemberResponse : ApiResponse
    {
        public bool AccountExists { get; set; }
        public bool IsLockedOut { get; set; }
        public bool InvalidCredentials { get; set; }
        public int MemberId { get; set; }       
        public string Token { get; set; } = string.Empty;        
        public bool IsDeepLinkLogin { get; set; }       
        public string RedirectUrl { get; set; } = string.Empty;
    }
}
