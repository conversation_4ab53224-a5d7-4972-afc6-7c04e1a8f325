﻿using Rewards4Platform.API.Data.Membership.Repositories;
using Rewards4Platform.API.Shared.Constants;
using Rewards4Platform.API.Shared.Responses;
using StackExchange.Redis.Extensions.Core.Abstractions;

namespace Rewards4Platform.API.Services
{
    public interface IMerchantService
    {
        Task<IList<int>> GetBlockedMerchantIds();
        Task<MerchantDetailsResponse> GetMerchantDetails(string merchantUrlName);
    }

    public class MerchantService : IMerchantService
    {
        private readonly ITenantService _tenantService;
        private readonly IMerchantRepository _merchantRepository;
        private readonly IRedisDatabase _redis;

        public MerchantService(IMerchantRepository merchantRepository, IRedisDatabase redis, ITenantService tenantService)
        {
            _merchantRepository = merchantRepository;
            _redis = redis;
            _tenantService = tenantService;
        }

        public async Task<IList<int>> GetBlockedMerchantIds()
        {
            var tenant = _tenantService.GetCurrentTenant();
            var applicationName = tenant.TenantConfiguration.ApplicationName;
            var clubId = tenant.TenantConfiguration.ClubId;

            var cacheKey = CacheKey.Merchant.SuppressedMerchants(applicationName, clubId);
            var cached = await _redis.GetWithRetryAsync<IList<int>>(cacheKey);
            if (cached is not null)
            {
                return cached;
            }

            var blockedMerchantIds = await _merchantRepository.GetBlockedMerchantIds(applicationName, clubId).ConfigureAwait(false);

            await _redis.AddAsync(cacheKey, blockedMerchantIds, CacheDuration.Merchant.SuppressedMerchants).ConfigureAwait(false);

            return blockedMerchantIds ?? new List<int>();
        }       

        public async Task<MerchantDetailsResponse> GetMerchantDetails(string merchantUrlName)
        {
            var tenant = _tenantService.GetCurrentTenant();
            var applicationName = tenant.TenantConfiguration.ApplicationName;
            var clubId = tenant.TenantConfiguration.ClubId;
            var cashOrPoints = tenant.TenantConfiguration.IsUsingCash ? tenant.TenantConfiguration.CashDescription : "points";

            var merchantDetailsResponse = new MerchantDetailsResponse();

            if (string.IsNullOrWhiteSpace(merchantUrlName))
            {
                merchantDetailsResponse =  new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "Merchant URL name cannot be null or empty."
                };

                return merchantDetailsResponse;
            }

            var isValidMerchant = await IsValidMerchant(merchantUrlName, applicationName, clubId);

            if (!isValidMerchant)
            {
                merchantDetailsResponse = new MerchantDetailsResponse
                {
                    IsSuccess = false,
                    Message = "The requested merchant is not found or suppressed."
                };

                return merchantDetailsResponse;
            }

            var cacheKey = CacheKey.Merchant.MerchantDetails(merchantUrlName, applicationName, clubId);
            var cachedMerchantDetailsResponse = await _redis.GetWithRetryAsync<MerchantDetailsResponse>(cacheKey);
            if (cachedMerchantDetailsResponse is not null)
            {
                merchantDetailsResponse = cachedMerchantDetailsResponse;

                return merchantDetailsResponse!;
            }            

            merchantDetailsResponse = await _merchantRepository.GetMerchantDetails(merchantUrlName).ConfigureAwait(false);

            if (merchantDetailsResponse.IsSuccess)
            {
                merchantDetailsResponse.IsCustomRetailer = false;

                merchantDetailsResponse.CustomDescription = $"Enjoy collecting {cashOrPoints} when you shop online with {merchantDetailsResponse.MerchantName}. Just remember to click through us here first so we can track your purchase and reward you the right amount of points.";

                var defaultHeaderBackgroundImageUrl = "https://neutstr4gblb.blob.core.windows.net/rewards4sport/generic-retailer-header.jpg";
                merchantDetailsResponse.HeaderBrackgroungImageUrl = string.IsNullOrWhiteSpace(merchantDetailsResponse.HeaderBrackgroungImageUrl) ? defaultHeaderBackgroundImageUrl : merchantDetailsResponse.HeaderBrackgroungImageUrl;

                await _redis.AddAsync(cacheKey, merchantDetailsResponse, CacheDuration.Merchant.MerchantDetails).ConfigureAwait(false);

                return merchantDetailsResponse;
            }

            // TODO:if merchant details not found in the database, fetch it from CMS

            return new MerchantDetailsResponse
            {
                IsSuccess = false,
                Message = "Merchant details not found."
            };
        }

        private async Task<bool> IsValidMerchant(string urlName, string applicationName, int clubId)
        {
            return await _merchantRepository.IsValidMerchant(urlName, applicationName, clubId).ConfigureAwait(false);
        }
    }
}
