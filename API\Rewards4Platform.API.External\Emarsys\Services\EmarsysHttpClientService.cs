﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.External.Emarsys.Services
{
    public interface IEmarsysHttpClientService
    {
        string Key { get; set; }
        string Secret { get; set; }
        Task<TResponse> Send<TResponse>(string method, string uri, Dictionary<string, string>? datalist = null, Dictionary<string, string>? emarsysFieldMappings = null, string? queryString = null, bool emailSummary = false);
        Task<TResponse> Send<TResponse>(string method, string uri, Dictionary<string, object> datalist, Dictionary<string, string>? emarsysFieldMappings = null);
        Task<TResponse> Send<TResponse>(string method, string uri, string postData);
    }

    public class EmarsysHttpClientService(HttpClient httpClient) : IEmarsysHttpClientService
    {
        public string Key { get; set; } = string.Empty;
        public string Secret { get; set; } = string.Empty;

        private readonly HttpClient _httpClient = httpClient;

        public async Task<TResponse> Send<TResponse>(string method, string uri, Dictionary<string, string>? datalist = null, Dictionary<string, string>? emarsysFieldMappings = null, string? queryString = null, bool emailSummary = false)
        {
            if (emailSummary)
            {
                var summaryResponse = await GetEmailSummary(method, uri, Key, Secret, datalist, emarsysFieldMappings, queryString);

                var summaryResponseContent = await summaryResponse.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<TResponse>(summaryResponseContent)!;
            }

            string postData = null;

            if (!string.IsNullOrEmpty(queryString))
            {
                uri = uri + queryString;
            }

            if (datalist != null)
            {
                postData = "{";

                foreach (var datakey in datalist.Keys)
                {
                    if (postData.Length > 1)
                    {
                        postData += ",";
                    }

                    postData += $"\"{datakey}\":";

                    if (datakey == "keyValues" || datakey == "fields")
                    {
                        postData += "[";
                    }

                    if (datakey != "contacts" && datakey != "fields")
                    {
                        postData += $"\"{datalist[datakey]}\"";
                    }
                    else
                    {
                        postData += datalist[datakey];
                    }

                    if (datakey == "keyValues" || datakey == "fields")
                    {
                        postData += "]";
                    }
                }

                postData += "}";
            }

            var nonce = GetRandomString(32);
            var timestamp = DateTime.UtcNow.ToString("o");
            var passwordDigest = System.Convert.ToBase64String(Encoding.UTF8.GetBytes(Sha1(nonce + timestamp + Secret)));
            var authHeader =
                String.Format(
                    "Username=\"{0}\", PasswordDigest=\"{1}\", Nonce=\"{2}\", Created=\"{3}\"  Content-type: application/json;charset=\"utf-8\"",
                    Key, passwordDigest, nonce, timestamp);

            _httpClient.DefaultRequestHeaders.Clear();

            // Prepare the request message
            var requestMessage = new HttpRequestMessage(new HttpMethod(method), uri);

            requestMessage.Headers.Add("X-WSSE", "UsernameToken " + authHeader);

            // Add the body for POST, PUT, and UPDATE methods
            if (method.Equals("POST", StringComparison.OrdinalIgnoreCase) ||
                method.Equals("PUT", StringComparison.OrdinalIgnoreCase) ||
                method.Equals("UPDATE", StringComparison.OrdinalIgnoreCase))
            {
                requestMessage.Content = new StringContent(postData, Encoding.ASCII, "application/json");
            }

            // Send the request and get the response
            var response = await _httpClient.SendAsync(requestMessage);

            // Ensure the response is successful
            response.EnsureSuccessStatusCode();

            // Read the response content
            var responseData = await response.Content.ReadAsStringAsync();

            if (emarsysFieldMappings != null)
            {
                foreach (var fieldMapping in emarsysFieldMappings)
                {
                    responseData = responseData.Replace($"\"{fieldMapping.Key}\"", $"\"{fieldMapping.Value}\"");
                }
            }
            // Deserialize and return the response
            return JsonConvert.DeserializeObject<TResponse>(responseData)!;
        }

        public async Task<TResponse> Send<TResponse>(string method, string uri, Dictionary<string, object> datalist, Dictionary<string, string>? emarsysFieldMappings = null)
        {
            var objectDataList = datalist.Keys.ToDictionary(key => key, key => datalist[key].ToString());
            return await Send<TResponse>(method, uri, objectDataList, emarsysFieldMappings);
        }        

        public async Task<TResponse> Send<TResponse>(string method, string uri, string postData)
        {
            var nonce = GetRandomString(32);
            var timestamp = DateTime.UtcNow.ToString("o");
            var passwordDigest =
                System.Convert.ToBase64String(Encoding.UTF8.GetBytes(Sha1(nonce + timestamp + Secret)));
            var authHeader =
                String.Format(
                    "Username=\"{0}\", PasswordDigest=\"{1}\", Nonce=\"{2}\", Created=\"{3}\"  Content-type: application/json;charset=\"utf-8\"",
                    Key, passwordDigest, nonce, timestamp);

            _httpClient.DefaultRequestHeaders.Clear();

            // Prepare the request message
            //var requestMessage = new HttpRequestMessage(new HttpMethod(method), "https://api.emarsys.net/api/v2/" + uri);
            var requestMessage = new HttpRequestMessage(new HttpMethod(method), uri);

            requestMessage.Headers.Add("X-WSSE", "UsernameToken " + authHeader);

            // Add the body for POST, PUT, and UPDATE methods
            if (method.Equals("POST", StringComparison.OrdinalIgnoreCase) ||
                method.Equals("PUT", StringComparison.OrdinalIgnoreCase) ||
                method.Equals("UPDATE", StringComparison.OrdinalIgnoreCase))
            {
                requestMessage.Content = new StringContent(postData, Encoding.ASCII, "application/json");
            }

            // Send the request and get the response
            var response = await _httpClient.SendAsync(requestMessage);

            // Ensure the response is successful
            response.EnsureSuccessStatusCode();
            // Read the response content
            var responseData = await response.Content.ReadAsStringAsync();

            return JsonConvert.DeserializeObject<TResponse>(responseData);
        }

        private static string GetRandomString(int length)
        {
            var random = new Random();
            var chars = new string[] { "0", "2", "3", "4", "5", "6", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "m", "n", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z" };
            var sb = new StringBuilder();
            for (var i = 0; i < length; i++)
            {
                sb.Append(chars[random.Next(chars.Length)]);
            }

            return sb.ToString();
        }

        private static string Sha1(string input)
        {
            var algorithm = SHA1.Create();
            var hashInBytes = algorithm.ComputeHash(Encoding.UTF8.GetBytes(input));
            return string.Join(string.Empty, Array.ConvertAll(hashInBytes, b => b.ToString("x2")));
        }

        private async Task<HttpResponseMessage> GetEmailSummary(string method, string uri, string key, string secret, Dictionary<string, string> datalist = null, Dictionary<string, string> emarsysFieldMappings = null, string queryString = null)
        {
            string postData = null;

            if (!string.IsNullOrEmpty(queryString))
            {
                uri = uri + queryString;
            }

            if (datalist != null)
            {
                postData = "{";

                foreach (var datakey in datalist.Keys)
                {
                    if (postData.Length > 1)
                    {
                        postData += ",";
                    }

                    postData += $"\"{datakey}\":";

                    if (datakey == "keyValues" || datakey == "fields")
                    {
                        postData += "[";
                    }

                    if (datakey != "contacts" && datakey != "fields")
                    {
                        postData += $"\"{datalist[datakey]}\"";
                    }
                    else
                    {
                        postData += datalist[datakey];
                    }

                    if (datakey == "keyValues" || datakey == "fields")
                    {
                        postData += "]";
                    }
                }

                postData += "}";
            }

            var nonce = GetRandomString(32);
            var timestamp = DateTime.UtcNow.ToString("o");
            var passwordDigest = Convert.ToBase64String(Encoding.UTF8.GetBytes(Sha1(nonce + timestamp + secret)));
            var authHeader =
                String.Format(
                    "Username=\"{0}\", PasswordDigest=\"{1}\", Nonce=\"{2}\", Created=\"{3}\"  Content-type: application/json;charset=\"utf-8\"",
                    key, passwordDigest, nonce, timestamp);

            _httpClient.DefaultRequestHeaders.Clear();

            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = _httpClient.BaseAddress,
                Headers =
            {
                { "X-WSSE", "UsernameToken " + authHeader }
            }
            };

            var response = await _httpClient.SendAsync(request);

            return response;
        }
    }
}
