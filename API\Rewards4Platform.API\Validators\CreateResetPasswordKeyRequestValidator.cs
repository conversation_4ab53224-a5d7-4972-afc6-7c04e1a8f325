﻿using FluentValidation;
using Rewards4Platform.API.Shared.Requests;

namespace Rewards4Platform.API.Validators
{
    public class CreateResetPasswordKeyRequestValidator : AbstractValidator<SendResetPasswordEmailRequest>
    {
        public CreateResetPasswordKeyRequestValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Email address is required.")
                .EmailAddress().WithMessage("Invalid email address format.")
                .MaximumLength(320).WithMessage("Email address cannot exceed 320 characters");
        }
    }
}
