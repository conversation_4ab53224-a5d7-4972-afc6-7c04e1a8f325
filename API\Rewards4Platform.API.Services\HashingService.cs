﻿using System.Security.Cryptography;
using System.Text;

namespace Rewards4Platform.API.Services
{
    public interface IHashingService
    {
        string HashPassword(string password, string base64Salt);        
        string HashEmail(string email);
        string Encrypt(string plainText, string key);
        Task<string> DecryptEmail(string encryptedEmail, string userName);
        string ComputeSHA256Hash(string sText);
        string GeneratePasswordSalt();
    }

    public class HashingService : IHashingService
    {
        private const int saltSize = 32;
        private const int keySize = 256;
        private const int blockSize = 128;
        private const int iterations = 1000;
        private static readonly HashAlgorithmName hashAlgorithm = HashAlgorithmName.SHA256;       

        public string HashPassword(string password, string base64Salt)
        {            
            var saltBytes = Convert.FromBase64String(base64Salt);
            var passwordBytes = Encoding.Unicode.GetBytes(password);

            var combined = new byte[saltBytes.Length + passwordBytes.Length];
            Buffer.BlockCopy(saltBytes, 0, combined, 0, saltBytes.Length);
            Buffer.BlockCopy(passwordBytes, 0, combined, saltBytes.Length, passwordBytes.Length);

            using (var sha1 = SHA1.Create())
            {
                var hash = sha1.ComputeHash(combined);
                return Convert.ToBase64String(hash);
            }
        }        

        public string HashEmail(string email)
        {
            email = email.Trim().ToLower();

            var hashedEmail = ComputeSHA256Hash(email);

            return hashedEmail;
        }

        public string Encrypt(string plainText, string key)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                throw new ArgumentNullException(nameof(plainText));
            }

            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentNullException(nameof(key));
            }
            
            key = ComputeSHA256Hash(key);

            using (var rfc2898DeriveBytes = new Rfc2898DeriveBytes(key, saltSize, iterations, HashAlgorithmName.SHA1))
            {
                var salt = rfc2898DeriveBytes.Salt;
                var keyBytes = rfc2898DeriveBytes.GetBytes(32);
                var ivBytes = rfc2898DeriveBytes.GetBytes(16);

                using (var aes = Aes.Create())
                {
                    aes.KeySize = keySize;
                    aes.BlockSize = 128;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.Key = keyBytes;
                    aes.IV = ivBytes;

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        MemoryStream memoryStream = null;
                        CryptoStream cryptoStream = null;
                        return WriteMemoryStream(plainText, ref salt, encryptor, ref memoryStream, ref cryptoStream);
                    }
                }
            }
        }

        public async Task<string> DecryptEmail(string encryptedEmail, string userName)
        {
            userName = ComputeSHA256Hash(userName);

            var allTheBytes = Convert.FromBase64String(encryptedEmail);
            var saltBytes = allTheBytes.Take(saltSize).ToArray();
            var ciphertextBytes = allTheBytes.Skip(saltSize).ToArray();

            string decrypted;

            using (var keyDerivationFunction = new Rfc2898DeriveBytes(
                password: userName,
                salt: saltBytes,
                iterations: iterations,
                hashAlgorithm: HashAlgorithmName.SHA1))
            {
                var keyBytes = keyDerivationFunction.GetBytes(32);
                var ivBytes = keyDerivationFunction.GetBytes(16);

                decrypted = await DecryptWithAES(ciphertextBytes, keyBytes, ivBytes);
            }

            return decrypted;
        }

        public string ComputeSHA256Hash(string sText)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(sText));
                return BitConverter.ToString(hashBytes).Replace("-", "");
            }
        }

        private string WriteMemoryStream(string plainText, ref byte[] saltBytes, ICryptoTransform encryptor, ref MemoryStream memoryStream, ref CryptoStream cryptoStream)
        {
            try
            {
                memoryStream = new MemoryStream();
                try
                {
                    cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write);
                    using (var streamWriter = new StreamWriter(cryptoStream))
                    {
                        streamWriter.Write(plainText);
                    }
                }
                finally
                {
                    cryptoStream?.Dispose();
                }
                var array = memoryStream.ToArray();
                Array.Resize(ref saltBytes, saltBytes.Length + array.Length);
                Array.Copy(array, 0, saltBytes, saltSize, array.Length);
                return Convert.ToBase64String(saltBytes);
            }
            finally
            {
                memoryStream?.Dispose();
            }
        }

        public string GeneratePasswordSalt()
        {
            var numArray = new byte[16];
            RandomNumberGenerator.Fill(numArray);
            return Convert.ToBase64String(numArray);
        }

        private async Task<string> DecryptWithAES(byte[] ciphertextBytes, byte[] keyBytes, byte[] ivBytes)
        {
            using var aes = Aes.Create();
            aes.KeySize = keySize;
            aes.BlockSize = blockSize;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var decryptor = aes.CreateDecryptor(keyBytes, ivBytes);
            using var memoryStream = new MemoryStream(ciphertextBytes);
            using var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read);
            using var streamReader = new StreamReader(cryptoStream, Encoding.UTF8);

            return await streamReader.ReadToEndAsync();
        }        
    }
}
