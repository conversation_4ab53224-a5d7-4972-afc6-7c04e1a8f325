﻿using Rewards4Platform.API.Data.Clubs.Repositories;

namespace Rewards4Platform.API.Services
{
    public interface IClubService
    {
        Task InsertClubMember(int memberId, int clubId);
    }

    public class ClubService : IClubService
    {
        private readonly IClubsRepostiory _clubsRepostiory;

        public ClubService(IClubsRepostiory clubsRepostiory)
        {
            _clubsRepostiory = clubsRepostiory;
        }

        public async Task InsertClubMember(int memberId, int clubId)
        {
            await _clubsRepostiory.InsertClubMember(memberId, clubId).ConfigureAwait(false);
        }
    }
}
