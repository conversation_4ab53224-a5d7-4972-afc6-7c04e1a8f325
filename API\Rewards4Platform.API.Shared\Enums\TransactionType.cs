﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.API.Shared.Enums
{
    public enum TransactionType
    {
        Redeem = 1,
        Generic = 2,
        InStoreShopping = 3,
        OnlineShopping = 4,
        InStoreShoppingRefund = 5,
        OnlineShoppingRefund = 6,
        RedeemRelatedRefund = 7,
        R4G = 8,
        R4GRefund = 9,
        CampaignPoints = 10,
        PointsAllocationCampaign = 11,
        ClubCredit = 13,
        ClubCreditRefund = 14,
        PointsExpiry = 16
    }
}
